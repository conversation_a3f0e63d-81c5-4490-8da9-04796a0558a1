[tool:pytest]
# Pytest configuration specifically for documentation testing

# Test discovery
testpaths = tests
python_files = test_documentation.py test_docs_*.py
python_classes = Test*
python_functions = test_*

# Markers for documentation tests
markers =
    docs: Documentation-related tests
    docs_build: Documentation build tests
    docs_quality: Documentation quality tests
    docs_integration: Documentation integration tests
    docs_coverage: Documentation coverage tests
    docs_examples: Documentation example tests
    docs_accessibility: Documentation accessibility tests
    docs_security: Documentation security tests
    docs_performance: Documentation performance tests
    slow: Slow-running tests (>30 seconds)
    network: Tests requiring network access

# Test execution options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --color=yes
    --disable-warnings
    --maxfail=5

# Coverage configuration for documentation tests
# (focuses on documentation-related code)
[coverage:run]
source = 
    app/
    scripts/docs_quality_check.py
    tests/test_documentation.py
    tests/test_docs_integration.py

omit = 
    */tests/*
    */migrations/*
    */venv/*
    */env/*
    */__pycache__/*
    */conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

show_missing = true
skip_covered = false
precision = 2

[coverage:html]
directory = reports/docs-coverage-html

[coverage:xml]
output = reports/docs-coverage.xml

# Documentation-specific test configuration
[docs]
# Minimum coverage thresholds
docstring_coverage_threshold = 80
link_check_threshold = 95
build_time_threshold = 60

# Quality score thresholds
overall_quality_threshold = 80
performance_score_threshold = 75

# Content requirements
required_sections = [
    "installation.rst",
    "quick-start-guide.rst", 
    "configuration.rst",
    "api/index.rst",
    "user-guides/index.rst"
]

# Excluded patterns for link checking
link_check_ignore = [
    "localhost",
    "127.0.0.1",
    "example.com",
    "test.example"
]

# Sphinx build options
sphinx_build_options = [
    "-W",  # Treat warnings as errors
    "-E",  # Don't use saved environment
    "--keep-going"  # Continue on errors where possible
]

# Documentation formats to test
test_formats = [
    "html",
    "linkcheck", 
    "coverage",
    "doctest"
]

# Performance benchmarks
max_build_time = 60  # seconds
max_total_size = 100  # MB
max_page_load_time = 3  # seconds

# Security checks
security_patterns = [
    "password\\s*[:=]\\s*[\"']?[^\"'\\s]+",
    "api[_-]?key\\s*[:=]\\s*[\"']?[^\"'\\s]+",
    "secret\\s*[:=]\\s*[\"']?[^\"'\\s]+",
    "token\\s*[:=]\\s*[\"']?[^\"'\\s]+"
]

# Content quality checks
quality_checks = [
    "check_headings",
    "check_links", 
    "check_code_examples",
    "check_spelling",
    "check_accessibility"
]
