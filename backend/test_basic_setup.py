#!/usr/bin/env python3
"""
Basic setup test to verify environment and identify issues
"""

import os
import sys
import warnings

def test_basic_imports():
    """Test basic imports work"""
    try:
        import pytest
        print("✓ pytest imported successfully")
        
        import sqlalchemy
        print("✓ SQLAlchemy imported successfully")
        
        import pydantic
        print("✓ Pydantic imported successfully")
        
        import fastapi
        print("✓ FastAPI imported successfully")
        
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_environment_variables():
    """Test environment variables"""
    required_vars = [
        'TESTING', 'SECRET_KEY', 'DATABASE_URL', 
        'REDIS_URL', 'NEO4J_URL', 'NEO4J_USER', 'NEO4J_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"✗ Missing environment variables: {missing_vars}")
        return False
    else:
        print("✓ All required environment variables are set")
        return True

def test_model_imports():
    """Test model imports"""
    try:
        # Set environment variables first
        os.environ.setdefault('TESTING', 'true')
        os.environ.setdefault('SECRET_KEY', 'test-secret-key')
        os.environ.setdefault('DATABASE_URL', 'postgresql://test:test@localhost:5432/test')
        os.environ.setdefault('REDIS_URL', 'redis://localhost:6379/1')
        os.environ.setdefault('NEO4J_URL', 'bolt://localhost:7687')
        os.environ.setdefault('NEO4J_USER', 'neo4j')
        os.environ.setdefault('NEO4J_PASSWORD', 'test_password')
        
        from app.db.models.cybersecurity_frameworks import CybersecurityFramework
        print("✓ Cybersecurity frameworks model imported successfully")
        
        from app.db.models.user import User
        print("✓ User model imported successfully")
        
        return True
    except Exception as e:
        print(f"✗ Model import error: {e}")
        return False

def capture_warnings():
    """Capture and display warnings"""
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        # Try to import main modules that might generate warnings
        try:
            import app.main
            import app.config
        except Exception as e:
            print(f"Warning during import: {e}")
        
        if w:
            print(f"\n📋 Captured {len(w)} warnings:")
            for warning in w:
                print(f"  - {warning.category.__name__}: {warning.message}")
                print(f"    File: {warning.filename}:{warning.lineno}")
        else:
            print("✓ No warnings captured")
        
        return w

if __name__ == "__main__":
    print("🧪 Running basic setup tests...\n")
    
    # Set environment variables
    os.environ['TESTING'] = 'true'
    os.environ['SECRET_KEY'] = 'test-secret-key'
    os.environ['DATABASE_URL'] = 'postgresql://test:test@localhost:5432/test'
    os.environ['REDIS_URL'] = 'redis://localhost:6379/1'
    os.environ['NEO4J_URL'] = 'bolt://localhost:7687'
    os.environ['NEO4J_USER'] = 'neo4j'
    os.environ['NEO4J_PASSWORD'] = 'test_password'
    
    results = []
    
    print("1. Testing basic imports...")
    results.append(test_basic_imports())
    
    print("\n2. Testing environment variables...")
    results.append(test_environment_variables())
    
    print("\n3. Testing model imports...")
    results.append(test_model_imports())
    
    print("\n4. Capturing warnings...")
    warnings_captured = capture_warnings()
    
    print(f"\n📊 Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("✅ Basic setup is working!")
        sys.exit(0)
    else:
        print("❌ Some tests failed")
        sys.exit(1)
