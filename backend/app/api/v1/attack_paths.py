"""Attack Path Analysis API Endpoints.

This module provides comprehensive REST API endpoints for attack path analysis,
blast radius calculation, and MITRE ATT&CK framework integration within the
Blast-Radius Security Tool.

The module supports:
- Attack path discovery and analysis between assets
- Blast radius calculation for compromised assets
- Attack scenario modeling and simulation
- MITRE ATT&CK technique mapping
- Graph-based security analysis and visualization

Example:
    Basic attack path analysis:
        POST /api/v1/attack-paths/analyze
        {
            "source_asset_id": "asset-123",
            "target_asset_ids": ["asset-456"],
            "max_path_length": 5
        }

    Blast radius calculation:
        POST /api/v1/attack-paths/blast-radius
        {
            "source_asset_id": "asset-123",
            "max_degrees": 3
        }

Attributes:
    router (APIRouter): FastAPI router for attack path endpoints.
    analyzer (Optional[AttackPathAnalyzer]): Global analyzer instance.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api.deps import get_db, get_current_active_user
from app.db.models.user import User
from app.services.attack_path_analyzer import AttackPathAnalyzer, AttackScenario
from app.services.graph_engine import AttackPath, BlastRadiusResult, PathType, AttackTechnique

router = APIRouter()


# Pydantic models for API
class AttackPathRequest(BaseModel):
    """Request model for attack path analysis.

    This model defines the parameters for analyzing attack paths between
    source and target assets within the security graph.

    Attributes:
        source_asset_id: Unique identifier of the source asset for analysis.
        target_asset_ids: Optional list of target asset IDs. If None, analyzes
            all high-value targets in the environment.
        max_path_length: Maximum number of hops in attack paths (1-10).
        max_paths_per_target: Maximum number of paths to return per target (1-20).

    Example:
        >>> request = AttackPathRequest(
        ...     source_asset_id="asset-123",
        ...     target_asset_ids=["asset-456", "asset-789"],
        ...     max_path_length=5,
        ...     max_paths_per_target=3
        ... )
    """
    source_asset_id: str = Field(..., description="Source asset ID for attack path analysis")
    target_asset_ids: Optional[List[str]] = Field(None, description="Target asset IDs (if None, analyzes all high-value targets)")
    max_path_length: int = Field(5, ge=1, le=10, description="Maximum path length to analyze")
    max_paths_per_target: int = Field(5, ge=1, le=20, description="Maximum paths per target")


class BlastRadiusRequest(BaseModel):
    """Request model for blast radius calculation.

    This model defines parameters for calculating the blast radius impact
    of a compromised asset within the security infrastructure.

    Attributes:
        source_asset_id: Unique identifier of the compromised source asset.
        max_degrees: Maximum degrees of separation to analyze (1-10).
            Higher values provide broader impact analysis but increase
            computational complexity.

    Example:
        >>> request = BlastRadiusRequest(
        ...     source_asset_id="critical-server-01",
        ...     max_degrees=3
        ... )
    """
    source_asset_id: str = Field(..., description="Source asset ID for blast radius calculation")
    max_degrees: int = Field(5, ge=1, le=10, description="Maximum degrees of separation")


class AttackScenarioRequest(BaseModel):
    """Request model for creating attack scenarios.

    This model defines parameters for creating comprehensive attack scenarios
    that model realistic threat actor behavior and objectives.

    Attributes:
        scenario_name: Human-readable name for the attack scenario.
        threat_actor: Name or identifier of the threat actor or group.
        entry_points: List of asset IDs representing potential entry points
            for the attack scenario.
        objectives: List of asset IDs representing target objectives or
            high-value assets the attacker aims to compromise.
        description: Optional detailed description of the scenario context
            and assumptions.

    Example:
        >>> request = AttackScenarioRequest(
        ...     scenario_name="Advanced Persistent Threat - Finance",
        ...     threat_actor="APT29",
        ...     entry_points=["email-server-01", "vpn-gateway-02"],
        ...     objectives=["database-finance", "backup-server"],
        ...     description="Targeted attack on financial systems"
        ... )
    """
    scenario_name: str = Field(..., description="Name of the attack scenario")
    threat_actor: str = Field(..., description="Threat actor or group")
    entry_points: List[str] = Field(..., description="List of potential entry point asset IDs")
    objectives: List[str] = Field(..., description="List of target objective asset IDs")
    description: Optional[str] = Field(None, description="Scenario description")


class AttackPathResponse(BaseModel):
    """Response model for attack path analysis results.

    This model represents a complete attack path with all associated
    risk metrics, techniques, and impact assessments.

    Attributes:
        path_id: Unique identifier for the attack path.
        source_asset_id: ID of the source asset where attack begins.
        target_asset_id: ID of the target asset being compromised.
        path_nodes: Ordered list of asset IDs in the attack path.
        path_type: Type of attack path (e.g., "LATERAL_MOVEMENT").
        attack_techniques: List of MITRE ATT&CK techniques used.
        risk_score: Overall risk score (0-100) for this path.
        likelihood: Probability of successful attack (0-1).
        impact_score: Business impact score (0-100) if compromised.
        blast_radius: Number of additional assets potentially affected.
        estimated_time: Estimated time in minutes to execute attack.
        required_privileges: List of privilege levels needed.
        detection_difficulty: Difficulty of detecting this attack (0-1).
        mitigation_cost: Estimated cost to mitigate this path.
        path_length: Number of hops in the attack path.
        criticality_score: Combined criticality score (0-100).
        criticality_level: Human-readable criticality level.
    """
    path_id: str
    source_asset_id: str
    target_asset_id: str
    path_nodes: List[str]
    path_type: str
    attack_techniques: List[str]
    risk_score: float
    likelihood: float
    impact_score: float
    blast_radius: int
    estimated_time: int
    required_privileges: List[str]
    detection_difficulty: float
    mitigation_cost: float
    path_length: int
    criticality_score: float
    criticality_level: str

    @classmethod
    def from_attack_path(cls, attack_path: AttackPath) -> "AttackPathResponse":
        """Create response model from AttackPath domain object.

        Args:
            attack_path: AttackPath domain object to convert.

        Returns:
            AttackPathResponse with all fields populated from domain object.
        """
        return cls(
            path_id=attack_path.path_id,
            source_asset_id=attack_path.source_asset_id,
            target_asset_id=attack_path.target_asset_id,
            path_nodes=attack_path.path_nodes,
            path_type=attack_path.path_type.value,
            attack_techniques=[t.value for t in attack_path.attack_techniques],
            risk_score=attack_path.risk_score,
            likelihood=attack_path.likelihood,
            impact_score=attack_path.impact_score,
            blast_radius=attack_path.blast_radius,
            estimated_time=attack_path.estimated_time,
            required_privileges=attack_path.required_privileges,
            detection_difficulty=attack_path.detection_difficulty,
            mitigation_cost=attack_path.mitigation_cost,
            path_length=attack_path.path_length,
            criticality_score=attack_path.criticality_score,
            criticality_level="CRITICAL" if attack_path.criticality_score >= 80 else
                            "HIGH" if attack_path.criticality_score >= 60 else
                            "MEDIUM" if attack_path.criticality_score >= 40 else "LOW"
        )


class BlastRadiusResponse(BaseModel):
    """Response model for blast radius calculation results.

    This model represents the comprehensive impact analysis of a compromised
    asset, including all affected systems and associated risk metrics.

    Attributes:
        source_asset_id: ID of the compromised source asset.
        affected_assets: List of all asset IDs potentially affected.
        impact_by_degree: Dictionary mapping degrees of separation to
            lists of affected asset IDs.
        total_impact_score: Overall impact score (0-100) for the blast radius.
        critical_assets_affected: List of critical asset IDs in blast radius.
        data_assets_affected: List of data asset IDs potentially compromised.
        service_disruption_score: Score (0-100) for service disruption impact.
        financial_impact: Estimated financial impact in dollars.
        compliance_impact: List of compliance frameworks potentially affected.
        recovery_time_estimate: Estimated recovery time in hours.
    """
    source_asset_id: str
    affected_assets: List[str]
    impact_by_degree: Dict[int, List[str]]
    total_impact_score: float
    critical_assets_affected: List[str]
    data_assets_affected: List[str]
    service_disruption_score: float
    financial_impact: float
    compliance_impact: List[str]
    recovery_time_estimate: int

    @classmethod
    def from_blast_radius_result(cls, result: BlastRadiusResult) -> "BlastRadiusResponse":
        """Create response model from BlastRadiusResult domain object.

        Args:
            result: BlastRadiusResult domain object to convert.

        Returns:
            BlastRadiusResponse with all fields populated from domain object.
        """
        return cls(
            source_asset_id=result.source_asset_id,
            affected_assets=list(result.affected_assets),
            impact_by_degree={k: list(v) for k, v in result.impact_by_degree.items()},
            total_impact_score=result.total_impact_score,
            critical_assets_affected=list(result.critical_assets_affected),
            data_assets_affected=list(result.data_assets_affected),
            service_disruption_score=result.service_disruption_score,
            financial_impact=result.financial_impact,
            compliance_impact=result.compliance_impact,
            recovery_time_estimate=result.recovery_time_estimate
        )


class AttackScenarioResponse(BaseModel):
    """Response model for attack scenario analysis results.

    This model represents a complete attack scenario with all discovered
    attack paths, risk assessments, and mitigation recommendations.

    Attributes:
        scenario_id: Unique identifier for the attack scenario.
        name: Human-readable name of the scenario.
        description: Detailed description of the scenario context.
        threat_actor: Name or identifier of the threat actor.
        attack_paths: List of all discovered attack paths in scenario.
        total_risk_score: Overall risk score (0-100) for the scenario.
        likelihood: Probability of scenario success (0-1).
        impact_score: Business impact score (0-100) if scenario succeeds.
        estimated_duration: Estimated time in hours to complete scenario.
        required_resources: List of resources needed by threat actor.
        detection_probability: Probability of detecting the scenario (0-1).
        mitigation_strategies: List of recommended mitigation strategies.
        criticality_level: Human-readable criticality level.
        created_at: Timestamp when scenario was created.
    """
    scenario_id: str
    name: str
    description: str
    threat_actor: str
    attack_paths: List[AttackPathResponse]
    total_risk_score: float
    likelihood: float
    impact_score: float
    estimated_duration: int
    required_resources: List[str]
    detection_probability: float
    mitigation_strategies: List[str]
    criticality_level: str
    created_at: datetime

    @classmethod
    def from_attack_scenario(cls, scenario: AttackScenario) -> "AttackScenarioResponse":
        """Create response model from AttackScenario domain object.

        Args:
            scenario: AttackScenario domain object to convert.

        Returns:
            AttackScenarioResponse with all fields populated from domain object.
        """
        return cls(
            scenario_id=scenario.scenario_id,
            name=scenario.name,
            description=scenario.description,
            threat_actor=scenario.threat_actor,
            attack_paths=[AttackPathResponse.from_attack_path(path) for path in scenario.attack_paths],
            total_risk_score=scenario.total_risk_score,
            likelihood=scenario.likelihood,
            impact_score=scenario.impact_score,
            estimated_duration=scenario.estimated_duration,
            required_resources=scenario.required_resources,
            detection_probability=scenario.detection_probability,
            mitigation_strategies=scenario.mitigation_strategies,
            criticality_level=scenario.criticality_level,
            created_at=scenario.created_at
        )


# Global analyzer instance (will be initialized on startup)
analyzer: Optional[AttackPathAnalyzer] = None


async def get_analyzer(db: Session = Depends(get_db)) -> AttackPathAnalyzer:
    """Get or create attack path analyzer instance.

    This dependency function provides a singleton AttackPathAnalyzer instance
    that is initialized with the current database session. The analyzer is
    created once and reused across requests for performance.

    Args:
        db: Database session dependency.

    Returns:
        AttackPathAnalyzer: Initialized analyzer instance ready for use.

    Note:
        The analyzer is initialized lazily on first access and cached
        globally for subsequent requests.
    """
    global analyzer
    if analyzer is None:
        analyzer = AttackPathAnalyzer(db)
        await analyzer.initialize_from_database()
    return analyzer


@router.post("/analyze", response_model=List[AttackPathResponse])
async def analyze_attack_paths(
    request: AttackPathRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Analyze attack paths from source to target assets.

    This endpoint performs comprehensive attack path analysis to identify
    potential routes an attacker could take to compromise target assets
    from a given source asset.

    Args:
        request: Attack path analysis parameters including source, targets,
            and analysis constraints.
        background_tasks: FastAPI background tasks for async processing.
        db: Database session for data access.
        current_user: Authenticated user making the request.
        analyzer: Attack path analyzer service instance.

    Returns:
        List[AttackPathResponse]: List of discovered attack paths with
            complete risk metrics and technique mappings.

    Raises:
        HTTPException: 404 if source or target assets not found,
            500 if analysis fails due to system error.

    Example:
        >>> response = await analyze_attack_paths(
        ...     AttackPathRequest(
        ...         source_asset_id="web-server-01",
        ...         target_asset_ids=["database-prod"],
        ...         max_path_length=5
        ...     )
        ... )
        >>> print(f"Found {len(response)} attack paths")
    """
    try:
        # Validate source asset exists
        if request.source_asset_id not in analyzer.graph_engine.asset_metadata:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Source asset {request.source_asset_id} not found"
            )
        
        # Validate target assets if provided
        if request.target_asset_ids:
            for target_id in request.target_asset_ids:
                if target_id not in analyzer.graph_engine.asset_metadata:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Target asset {target_id} not found"
                    )
        
        # Perform analysis
        attack_paths = await analyzer.analyze_attack_paths(
            source_asset_id=request.source_asset_id,
            target_asset_ids=request.target_asset_ids,
            max_path_length=request.max_path_length,
            max_paths_per_target=request.max_paths_per_target
        )
        
        # Convert to response format
        response_paths = [AttackPathResponse.from_attack_path(path) for path in attack_paths]
        
        return response_paths
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Attack path analysis failed: {str(e)}"
        )


@router.post("/blast-radius", response_model=BlastRadiusResponse)
async def calculate_blast_radius(
    request: BlastRadiusRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Calculate blast radius from a compromised asset.

    This endpoint calculates the potential impact and blast radius if a
    specific asset is compromised, showing all assets that could be
    affected through various attack vectors.

    Args:
        request: Blast radius calculation parameters including source asset
            and analysis depth.
        db: Database session for data access.
        current_user: Authenticated user making the request.
        analyzer: Attack path analyzer service instance.

    Returns:
        BlastRadiusResponse: Comprehensive blast radius analysis including
            affected assets, impact scores, and recovery estimates.

    Raises:
        HTTPException: 404 if source asset not found,
            500 if calculation fails due to system error.

    Example:
        >>> response = await calculate_blast_radius(
        ...     BlastRadiusRequest(
        ...         source_asset_id="critical-server-01",
        ...         max_degrees=3
        ...     )
        ... )
        >>> print(f"Blast radius affects {len(response.affected_assets)} assets")
    """
    try:
        # Validate source asset exists
        if request.source_asset_id not in analyzer.graph_engine.asset_metadata:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Source asset {request.source_asset_id} not found"
            )
        
        # Calculate blast radius
        blast_result = await analyzer.calculate_blast_radius(
            source_asset_id=request.source_asset_id,
            max_degrees=request.max_degrees
        )
        
        # Convert to response format
        response = BlastRadiusResponse.from_blast_radius_result(blast_result)
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Blast radius calculation failed: {str(e)}"
        )


@router.post("/scenarios", response_model=AttackScenarioResponse)
async def create_attack_scenario(
    request: AttackScenarioRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Create and analyze a comprehensive attack scenario.

    This endpoint creates a complete attack scenario that models realistic
    threat actor behavior, including multiple entry points, objectives,
    and attack paths between them.

    Args:
        request: Attack scenario parameters including name, threat actor,
            entry points, and objectives.
        db: Database session for data access.
        current_user: Authenticated user making the request.
        analyzer: Attack path analyzer service instance.

    Returns:
        AttackScenarioResponse: Complete attack scenario with all discovered
            paths, risk assessments, and mitigation strategies.

    Raises:
        HTTPException: 404 if entry points or objectives not found,
            500 if scenario creation fails due to system error.

    Example:
        >>> response = await create_attack_scenario(
        ...     AttackScenarioRequest(
        ...         scenario_name="APT Campaign - Finance",
        ...         threat_actor="APT29",
        ...         entry_points=["email-server"],
        ...         objectives=["database-finance"]
        ...     )
        ... )
        >>> print(f"Scenario has {len(response.attack_paths)} attack paths")
    """
    try:
        # Validate entry points
        for entry_point in request.entry_points:
            if entry_point not in analyzer.graph_engine.asset_metadata:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Entry point asset {entry_point} not found"
                )
        
        # Validate objectives
        for objective in request.objectives:
            if objective not in analyzer.graph_engine.asset_metadata:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Objective asset {objective} not found"
                )
        
        # Create attack scenario
        scenario = await analyzer.create_attack_scenario(
            scenario_name=request.scenario_name,
            threat_actor=request.threat_actor,
            entry_points=request.entry_points,
            objectives=request.objectives
        )
        
        # Convert to response format
        response = AttackScenarioResponse.from_attack_scenario(scenario)
        
        return response
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Attack scenario creation failed: {str(e)}"
        )


@router.get("/scenarios/{scenario_id}", response_model=AttackScenarioResponse)
async def get_attack_scenario(
    scenario_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Get a specific attack scenario by ID.

    Retrieves a previously created attack scenario with all its associated
    attack paths, risk metrics, and analysis results.

    Args:
        scenario_id: Unique identifier of the attack scenario to retrieve.
        db: Database session for data access.
        current_user: Authenticated user making the request.
        analyzer: Attack path analyzer service instance.

    Returns:
        AttackScenarioResponse: Complete attack scenario data including
            all paths and risk assessments.

    Raises:
        HTTPException: 404 if scenario not found in cache.
    """
    if scenario_id not in analyzer.scenario_cache:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Attack scenario {scenario_id} not found"
        )
    
    scenario = analyzer.scenario_cache[scenario_id]
    return AttackScenarioResponse.from_attack_scenario(scenario)


@router.get("/mitre-mapping/{path_id}")
async def get_mitre_attack_mapping(
    path_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Get MITRE ATT&CK framework mapping for an attack path."""
    # Find attack path in cached scenarios
    attack_path = None
    for scenario in analyzer.scenario_cache.values():
        for path in scenario.attack_paths:
            if path.path_id == path_id:
                attack_path = path
                break
        if attack_path:
            break
    
    if not attack_path:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Attack path {path_id} not found"
        )
    
    mitre_mapping = analyzer.get_mitre_attack_mapping(attack_path)
    return mitre_mapping


@router.get("/graph/statistics")
async def get_graph_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Get comprehensive graph statistics."""
    stats = analyzer.graph_engine.get_graph_statistics()
    return {
        "timestamp": datetime.utcnow().isoformat(),
        "statistics": stats,
        "message": "Graph statistics retrieved successfully"
    }


@router.post("/graph/refresh")
async def refresh_graph(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Refresh graph data from database."""
    global analyzer
    
    def refresh_task():
        global analyzer
        analyzer = AttackPathAnalyzer(db)
        # Note: This would need to be made async-safe in production
    
    background_tasks.add_task(refresh_task)
    
    return {
        "message": "Graph refresh initiated",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/export")
async def export_analysis_results(
    format: str = Query("json", description="Export format (json)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Export analysis results in various formats."""
    try:
        export_data = analyzer.export_analysis_results(format)
        
        return {
            "format": format,
            "data": export_data,
            "timestamp": datetime.utcnow().isoformat(),
            "message": "Analysis results exported successfully"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Export failed: {str(e)}"
        )


@router.delete("/cache")
async def clear_analysis_cache(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    analyzer: AttackPathAnalyzer = Depends(get_analyzer)
):
    """Clear all analysis caches."""
    analyzer.clear_cache()
    
    return {
        "message": "Analysis cache cleared successfully",
        "timestamp": datetime.utcnow().isoformat()
    }
