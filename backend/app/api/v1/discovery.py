"""Discovery Job Management API Endpoints.

This module provides comprehensive REST API endpoints for managing asset discovery
jobs within the Blast-Radius Security Tool, supporting multiple discovery engines
and cloud platforms.

The module supports:
- Discovery job lifecycle management (create, start, complete, fail)
- Multi-cloud discovery (AWS, Azure, GCP) with platform-specific configurations
- API discovery using specialized tools (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ner)
- Network discovery using nmap and other network scanning tools
- Job status tracking and result management
- Comprehensive filtering and pagination for job listings

Example:
    Basic discovery job creation:
        POST /api/v1/discovery/jobs
        {
            "name": "Production AWS Discovery",
            "description": "Discover all AWS resources in production",
            "job_type": "aws_discovery",
            "configuration": {
                "region": "us-east-1",
                "services": ["ec2", "rds", "s3"]
            }
        }

    Cloud-specific discovery:
        POST /api/v1/discovery/cloud/aws
        {
            "region": "us-west-2",
            "services": ["ec2", "rds"],
            "credentials_profile": "production"
        }

Discovery Features:
- Automated asset discovery across multiple platforms
- Real-time job status monitoring and progress tracking
- Comprehensive error handling and retry mechanisms
- Detailed execution logs and discovery metrics
- Integration with asset management and relationship mapping

Attributes:
    router (APIRouter): FastAPI router for discovery endpoints.
    logger (Logger): Logger instance for discovery operations.
"""

import logging
import math
from typing import Any, Dict, List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.db.models.user import User
from app.db.session import get_db
from app.dependencies import get_current_active_user
from app.schemas.asset import (
    DiscoveryJobCreate,
    DiscoveryJobUpdate,
    DiscoveryJobResponse,
    DiscoveryJobListResponse,
)
from app.services.asset_service import (
    AssetService,
    DiscoveryJobNotFoundError,
)
from app.services.discovery_orchestrator import get_discovery_orchestrator

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/jobs", response_model=DiscoveryJobResponse, status_code=status.HTTP_201_CREATED)
async def create_discovery_job(
    job_data: DiscoveryJobCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Create a new discovery job.

    This endpoint creates a new discovery job with the specified configuration
    and returns the job details for tracking and management.

    Args:
        job_data: Discovery job creation data including name, type, and configuration.
        db: Database session for data persistence.
        current_user: Authenticated user creating the job.

    Returns:
        DiscoveryJobResponse: Created discovery job with ID and initial status.

    Raises:
        HTTPException: 500 if job creation fails due to system error.

    Example:
        >>> response = await create_discovery_job(
        ...     DiscoveryJobCreate(
        ...         name="AWS Production Discovery",
        ...         job_type="aws_discovery",
        ...         configuration={"region": "us-east-1"}
        ...     )
        ... )
        >>> print(response.id)
    """
    try:
        asset_service = AssetService(db)
        job = asset_service.create_discovery_job(job_data)
        return job
    except Exception as e:
        logger.error(f"Error creating discovery job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create discovery job"
        )


@router.get("/jobs/{job_id}", response_model=DiscoveryJobResponse)
async def get_discovery_job(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Get a discovery job by ID."""
    try:
        asset_service = AssetService(db)
        job = asset_service.get_discovery_job(job_id)
        return job
    except DiscoveryJobNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Discovery job not found"
        )


@router.put("/jobs/{job_id}", response_model=DiscoveryJobResponse)
async def update_discovery_job(
    job_id: uuid.UUID,
    job_data: DiscoveryJobUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Update a discovery job."""
    try:
        asset_service = AssetService(db)
        job = asset_service.update_discovery_job(job_id, job_data)
        return job
    except DiscoveryJobNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Discovery job not found"
        )
    except Exception as e:
        logger.error(f"Error updating discovery job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update discovery job"
        )


@router.delete("/jobs/{job_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_discovery_job(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> None:
    """Delete a discovery job."""
    try:
        asset_service = AssetService(db)
        asset_service.delete_discovery_job(job_id)
    except DiscoveryJobNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Discovery job not found"
        )
    except Exception as e:
        logger.error(f"Error deleting discovery job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete discovery job"
        )


@router.get("/jobs", response_model=DiscoveryJobListResponse)
async def get_discovery_jobs(
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    status: Optional[str] = Query(None, description="Filter by job status"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Get discovery jobs with filtering and pagination.

    This endpoint retrieves discovery jobs with comprehensive filtering
    options and pagination support for efficient job management.

    Args:
        job_type: Optional filter by job type (aws_discovery, azure_discovery, etc.).
        status: Optional filter by job status (pending, running, completed, failed).
        page: Page number for pagination (1-based).
        size: Number of jobs per page (1-100).
        db: Database session for data access.
        current_user: Authenticated user making the request.

    Returns:
        DiscoveryJobListResponse: Paginated list of discovery jobs with metadata.

    Raises:
        HTTPException: 400 if invalid status provided,
            500 if retrieval fails due to system error.

    Example:
        >>> response = await get_discovery_jobs(
        ...     job_type="aws_discovery",
        ...     status="completed",
        ...     page=1,
        ...     size=20
        ... )
        >>> print(f"Found {response.total} jobs")
    """
    try:
        asset_service = AssetService(db)
        
        # Convert string status to enum if provided
        status_enum = None
        if status:
            from app.db.models.asset import DiscoveryJobStatus
            try:
                status_enum = DiscoveryJobStatus(status)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid job status: {status}"
                )
        
        jobs, total = asset_service.get_discovery_jobs(
            job_type=job_type,
            status=status_enum,
            page=page,
            size=size
        )
        
        pages = math.ceil(total / size) if total > 0 else 0
        
        return DiscoveryJobListResponse(
            jobs=jobs,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
    except Exception as e:
        logger.error(f"Error getting discovery jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get discovery jobs"
        )


@router.post("/jobs/{job_id}/start", response_model=DiscoveryJobResponse)
async def start_discovery_job(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Start a discovery job."""
    try:
        asset_service = AssetService(db)
        job = asset_service.start_discovery_job(job_id, executor=current_user.username)
        return job
    except DiscoveryJobNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Discovery job not found"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error starting discovery job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start discovery job"
        )


@router.post("/jobs/{job_id}/complete", response_model=DiscoveryJobResponse)
async def complete_discovery_job(
    job_id: uuid.UUID,
    completion_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Complete a discovery job with results."""
    try:
        asset_service = AssetService(db)
        
        # Extract completion data
        assets_discovered = completion_data.get("assets_discovered", 0)
        assets_updated = completion_data.get("assets_updated", 0)
        relationships_discovered = completion_data.get("relationships_discovered", 0)
        errors_count = completion_data.get("errors_count", 0)
        execution_log = completion_data.get("execution_log")
        error_details = completion_data.get("error_details")
        
        job = asset_service.complete_discovery_job(
            job_id=job_id,
            assets_discovered=assets_discovered,
            assets_updated=assets_updated,
            relationships_discovered=relationships_discovered,
            errors_count=errors_count,
            execution_log=execution_log,
            error_details=error_details
        )
        return job
    except DiscoveryJobNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Discovery job not found"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error completing discovery job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete discovery job"
        )


@router.post("/jobs/{job_id}/fail", response_model=DiscoveryJobResponse)
async def fail_discovery_job(
    job_id: uuid.UUID,
    failure_data: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Mark a discovery job as failed."""
    try:
        asset_service = AssetService(db)
        
        error_details = failure_data.get("error_details")
        
        job = asset_service.fail_discovery_job(
            job_id=job_id,
            error_details=error_details
        )
        return job
    except DiscoveryJobNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Discovery job not found"
        )
    except Exception as e:
        logger.error(f"Error failing discovery job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to mark discovery job as failed"
        )


# Discovery job execution endpoints for different discovery types
@router.post("/cloud/aws", response_model=DiscoveryJobResponse, status_code=status.HTTP_201_CREATED)
async def start_aws_discovery(
    discovery_config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Start AWS cloud discovery job.

    This endpoint creates and immediately starts an AWS cloud discovery job
    to automatically discover and inventory AWS resources.

    Args:
        discovery_config: AWS discovery configuration including region,
            services to scan, credentials profile, and other AWS-specific settings.
        db: Database session for data persistence.
        current_user: Authenticated user starting the discovery.

    Returns:
        DiscoveryJobResponse: Created and started AWS discovery job.

    Raises:
        HTTPException: 500 if job creation or startup fails.

    Example:
        >>> response = await start_aws_discovery({
        ...     "region": "us-east-1",
        ...     "services": ["ec2", "rds", "s3"],
        ...     "credentials_profile": "production"
        ... })
        >>> print(f"Started AWS discovery job: {response.id}")
    """
    try:
        asset_service = AssetService(db)
        
        job_data = DiscoveryJobCreate(
            name=f"AWS Discovery - {discovery_config.get('region', 'all-regions')}",
            description="Automated AWS cloud asset discovery",
            job_type="aws_discovery",
            configuration=discovery_config,
            executor=current_user.username
        )
        
        job = asset_service.create_discovery_job(job_data)

        # Start the discovery job using the orchestrator
        orchestrator = get_discovery_orchestrator(db)
        success = await orchestrator.start_discovery_job(job.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start discovery job"
            )

        return job
    except Exception as e:
        logger.error(f"Error starting AWS discovery: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start AWS discovery"
        )


@router.post("/cloud/azure", response_model=DiscoveryJobResponse, status_code=status.HTTP_201_CREATED)
async def start_azure_discovery(
    discovery_config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Start Azure cloud discovery job."""
    try:
        asset_service = AssetService(db)
        
        job_data = DiscoveryJobCreate(
            name=f"Azure Discovery - {discovery_config.get('subscription_id', 'all-subscriptions')}",
            description="Automated Azure cloud asset discovery",
            job_type="azure_discovery",
            configuration=discovery_config,
            executor=current_user.username
        )
        
        job = asset_service.create_discovery_job(job_data)

        # Start the discovery job using the orchestrator
        orchestrator = get_discovery_orchestrator(db)
        success = await orchestrator.start_discovery_job(job.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start discovery job"
            )

        return job
    except Exception as e:
        logger.error(f"Error starting Azure discovery: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start Azure discovery"
        )


@router.post("/cloud/gcp", response_model=DiscoveryJobResponse, status_code=status.HTTP_201_CREATED)
async def start_gcp_discovery(
    discovery_config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Start GCP cloud discovery job."""
    try:
        asset_service = AssetService(db)
        
        job_data = DiscoveryJobCreate(
            name=f"GCP Discovery - {discovery_config.get('project_id', 'all-projects')}",
            description="Automated GCP cloud asset discovery",
            job_type="gcp_discovery",
            configuration=discovery_config,
            executor=current_user.username
        )
        
        job = asset_service.create_discovery_job(job_data)

        # Start the discovery job using the orchestrator
        orchestrator = get_discovery_orchestrator(db)
        success = await orchestrator.start_discovery_job(job.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start discovery job"
            )

        return job
    except Exception as e:
        logger.error(f"Error starting GCP discovery: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start GCP discovery"
        )


@router.post("/api/akto", response_model=DiscoveryJobResponse, status_code=status.HTTP_201_CREATED)
async def start_akto_discovery(
    discovery_config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Start Akto API discovery job."""
    try:
        asset_service = AssetService(db)
        
        job_data = DiscoveryJobCreate(
            name=f"Akto API Discovery - {discovery_config.get('target', 'unknown')}",
            description="Automated API discovery using Akto",
            job_type="akto_discovery",
            configuration=discovery_config,
            executor=current_user.username
        )
        
        job = asset_service.create_discovery_job(job_data)

        # Start the discovery job using the orchestrator
        orchestrator = get_discovery_orchestrator(db)
        success = await orchestrator.start_discovery_job(job.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start discovery job"
            )

        return job
    except Exception as e:
        logger.error(f"Error starting Akto discovery: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start Akto discovery"
        )


@router.post("/api/kiterunner", response_model=DiscoveryJobResponse, status_code=status.HTTP_201_CREATED)
async def start_kiterunner_discovery(
    discovery_config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Start Kiterunner API discovery job."""
    try:
        asset_service = AssetService(db)
        
        job_data = DiscoveryJobCreate(
            name=f"Kiterunner API Discovery - {discovery_config.get('target', 'unknown')}",
            description="Automated API endpoint discovery using Kiterunner",
            job_type="kiterunner_discovery",
            configuration=discovery_config,
            executor=current_user.username
        )
        
        job = asset_service.create_discovery_job(job_data)

        # Start the discovery job using the orchestrator
        orchestrator = get_discovery_orchestrator(db)
        success = await orchestrator.start_discovery_job(job.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start discovery job"
            )

        return job
    except Exception as e:
        logger.error(f"Error starting Kiterunner discovery: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start Kiterunner discovery"
        )


@router.post("/network", response_model=DiscoveryJobResponse, status_code=status.HTTP_201_CREATED)
async def start_network_discovery(
    discovery_config: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Any:
    """Start network discovery job."""
    try:
        asset_service = AssetService(db)

        job_data = DiscoveryJobCreate(
            name=f"Network Discovery - {discovery_config.get('target_networks', 'unknown')}",
            description="Automated network asset discovery using nmap",
            job_type="network_discovery",
            configuration=discovery_config,
            executor=current_user.username
        )

        job = asset_service.create_discovery_job(job_data)

        # Start the discovery job using the orchestrator
        orchestrator = get_discovery_orchestrator(db)
        success = await orchestrator.start_discovery_job(job.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to start discovery job"
            )

        return job
    except Exception as e:
        logger.error(f"Error starting network discovery: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start network discovery"
        )


@router.post("/jobs/{job_id}/cancel", status_code=status.HTTP_200_OK)
async def cancel_discovery_job(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Cancel a running discovery job."""
    try:
        orchestrator = get_discovery_orchestrator(db)
        success = await orchestrator.cancel_discovery_job(job_id)

        if success:
            return {"message": f"Discovery job {job_id} cancelled successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Job is not running or cannot be cancelled"
            )
    except Exception as e:
        logger.error(f"Error cancelling discovery job {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel discovery job"
        )


@router.get("/jobs/{job_id}/status", response_model=Dict[str, Any])
async def get_job_status(
    job_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get detailed status of a discovery job."""
    try:
        orchestrator = get_discovery_orchestrator(db)
        status_info = await orchestrator.get_job_status(job_id)

        if "error" in status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=status_info["error"]
            )

        return status_info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )


@router.get("/statistics", response_model=Dict[str, Any])
async def get_discovery_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """Get discovery statistics."""
    try:
        orchestrator = get_discovery_orchestrator(db)
        stats = await orchestrator.get_discovery_statistics()
        return stats
    except Exception as e:
        logger.error(f"Error getting discovery statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get discovery statistics"
        )
