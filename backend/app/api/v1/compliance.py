"""Compliance Framework API Endpoints.

This module provides comprehensive REST API endpoints for compliance management
within the Blast-Radius Security Tool, supporting multiple regulatory frameworks
and enterprise compliance requirements.

The module supports:
- GDPR compliance operations and data subject rights management
- Comprehensive audit log access and reporting capabilities
- Least privilege access management with approval workflows
- Multi-framework compliance reporting (GDPR, SOX, HIPAA, PCI-DSS)
- Data breach notification and incident management
- Consent management and processing record maintenance
- Access request workflows with automated approval processes

Example:
    GDPR consent management:
        POST /api/v1/compliance/gdpr/consent
        {
            "purpose": "ANALYTICS",
            "legal_basis": "CONSENT",
            "consent_text": "I agree to analytics processing",
            "version": "1.0"
        }

    Access request workflow:
        POST /api/v1/compliance/access/request
        {
            "resource_type": "database",
            "access_types": ["READ"],
            "justification": "BUSINESS_NEED",
            "business_justification": "Monthly reporting requirements"
        }

Compliance Features:
- GDPR Article 30 record of processing activities
- Automated data breach notification workflows
- Least privilege access with time-based expiration
- Comprehensive audit trails with integrity verification
- Multi-framework compliance reporting and dashboards

Attributes:
    router (APIRouter): FastAPI router for compliance endpoints.
    security (HTTPBearer): HTTP Bearer token security scheme.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from fastapi.security import HTTPBearer
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.core.auth import get_current_user, require_permission
from app.core.permissions import Permission
from app.db.models.user import User
from app.services.gdpr_compliance_service import (
    GDPRComplianceService, 
    DataSubjectRightType, 
    ProcessingPurpose, 
    LegalBasisType
)
from app.services.enhanced_audit_service import (
    EnhancedAuditService, 
    AuditEventType, 
    ComplianceFramework
)
from app.services.least_privilege_service import (
    LeastPrivilegeService, 
    AccessType, 
    AccessJustification
)
from app.services.notification_service import NotificationService

router = APIRouter(prefix="/compliance", tags=["compliance"])
security = HTTPBearer()


# Pydantic models for request/response
class ConsentRequest(BaseModel):
    """Request model for recording GDPR consent.

    This model defines the structure for recording user consent
    for data processing activities under GDPR compliance.

    Attributes:
        purpose: The purpose for which data will be processed.
        legal_basis: Legal basis for processing under GDPR.
        consent_text: Human-readable consent text shown to user.
        version: Version of the consent text for tracking changes.
        expires_in_days: Optional expiration period for consent in days.

    Example:
        >>> consent = ConsentRequest(
        ...     purpose=ProcessingPurpose.ANALYTICS,
        ...     legal_basis=LegalBasisType.CONSENT,
        ...     consent_text="I agree to analytics processing",
        ...     version="1.0",
        ...     expires_in_days=365
        ... )
    """
    purpose: ProcessingPurpose
    legal_basis: LegalBasisType
    consent_text: str
    version: str
    expires_in_days: Optional[int] = None


class DataSubjectRequestModel(BaseModel):
    """Request model for GDPR data subject rights requests.

    This model defines the structure for submitting data subject
    requests under GDPR Articles 15-22 (access, rectification, erasure, etc.).

    Attributes:
        request_type: Type of data subject right being exercised.
        description: Detailed description of the request.
        verification_method: Method for verifying the data subject's identity.

    Example:
        >>> request = DataSubjectRequestModel(
        ...     request_type=DataSubjectRightType.ACCESS,
        ...     description="Request copy of all personal data",
        ...     verification_method="email"
        ... )
    """
    request_type: DataSubjectRightType
    description: str
    verification_method: str = "email"


class AccessRequestModel(BaseModel):
    """Request model for least privilege access requests.

    This model defines the structure for requesting access to resources
    under the least privilege access management system.

    Attributes:
        resource_type: Type of resource being requested (database, server, etc.).
        resource_id: Optional specific resource identifier.
        access_types: List of access types being requested (READ, WRITE, etc.).
        justification: Predefined justification category for the request.
        business_justification: Detailed business justification text.
        duration_hours: Optional duration for temporary access in hours.
        emergency: Whether this is an emergency access request.

    Example:
        >>> request = AccessRequestModel(
        ...     resource_type="database",
        ...     resource_id="prod-db-01",
        ...     access_types=[AccessType.READ],
        ...     justification=AccessJustification.BUSINESS_NEED,
        ...     business_justification="Monthly financial reporting",
        ...     duration_hours=24
        ... )
    """
    resource_type: str
    resource_id: Optional[str] = None
    access_types: List[AccessType]
    justification: AccessJustification
    business_justification: str
    duration_hours: Optional[int] = None
    emergency: bool = False


class PrivilegeEscalationRequest(BaseModel):
    target_role: str
    justification: str
    duration_hours: Optional[int] = None
    emergency: bool = False


class DataBreachReport(BaseModel):
    description: str
    affected_data_subjects: int
    data_categories_affected: List[str]
    likely_consequences: str
    measures_taken: List[str]
    risk_level: str = Field(..., regex="^(low|medium|high)$")


class ComplianceReportRequest(BaseModel):
    framework: ComplianceFramework
    start_date: datetime
    end_date: datetime


# Dependency injection
def get_gdpr_service(db: Session = Depends(get_db)) -> GDPRComplianceService:
    """Get GDPR compliance service instance.

    This dependency function provides a GDPRComplianceService instance
    with notification service integration for GDPR operations.

    Args:
        db: Database session dependency.

    Returns:
        GDPRComplianceService: Service instance for GDPR compliance operations.
    """
    notification_service = NotificationService()
    return GDPRComplianceService(db, notification_service)


def get_audit_service(db: Session = Depends(get_db)) -> EnhancedAuditService:
    """Get enhanced audit service instance.

    This dependency function provides an EnhancedAuditService instance
    for audit logging and compliance reporting operations.

    Args:
        db: Database session dependency.

    Returns:
        EnhancedAuditService: Service instance for audit operations.
    """
    return EnhancedAuditService(db)


def get_privilege_service(
    db: Session = Depends(get_db),
    audit_service: EnhancedAuditService = Depends(get_audit_service)
) -> LeastPrivilegeService:
    """Get least privilege service instance.

    This dependency function provides a LeastPrivilegeService instance
    with audit service integration for access management operations.

    Args:
        db: Database session dependency.
        audit_service: Enhanced audit service dependency.

    Returns:
        LeastPrivilegeService: Service instance for privilege management.
    """
    return LeastPrivilegeService(db, audit_service)


# GDPR Compliance Endpoints
@router.post("/gdpr/consent")
async def record_consent(
    consent_request: ConsentRequest,
    current_user: User = Depends(get_current_user),
    gdpr_service: GDPRComplianceService = Depends(get_gdpr_service)
):
    """Record user consent for data processing under GDPR.

    This endpoint records explicit user consent for data processing
    activities, maintaining compliance with GDPR Article 7 requirements.

    Args:
        consent_request: Consent details including purpose and legal basis.
        current_user: Authenticated user providing consent.
        gdpr_service: GDPR compliance service instance.

    Returns:
        Dict containing consent record ID and success status.

    Raises:
        HTTPException: 400 if consent recording fails due to validation errors.

    Example:
        >>> response = await record_consent(
        ...     ConsentRequest(
        ...         purpose=ProcessingPurpose.ANALYTICS,
        ...         legal_basis=LegalBasisType.CONSENT,
        ...         consent_text="I agree to analytics processing",
        ...         version="1.0"
        ...     )
        ... )
        >>> print(response["consent_id"])
    """
    try:
        consent_record = await gdpr_service.record_consent(
            user_id=str(current_user.id),
            purpose=consent_request.purpose,
            legal_basis=consent_request.legal_basis,
            consent_text=consent_request.consent_text,
            version=consent_request.version,
            ip_address="127.0.0.1",  # Would get from request
            user_agent="API",  # Would get from request
            expires_in_days=consent_request.expires_in_days
        )
        
        return {
            "status": "success",
            "consent_id": consent_record.consent_id,
            "message": "Consent recorded successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/gdpr/consent/{purpose}")
async def withdraw_consent(
    purpose: ProcessingPurpose,
    current_user: User = Depends(get_current_user),
    gdpr_service: GDPRComplianceService = Depends(get_gdpr_service)
):
    """Withdraw user consent for data processing under GDPR.

    This endpoint allows users to withdraw previously given consent
    for specific data processing purposes, as required by GDPR Article 7(3).

    Args:
        purpose: The processing purpose for which to withdraw consent.
        current_user: Authenticated user withdrawing consent.
        gdpr_service: GDPR compliance service instance.

    Returns:
        Dict containing withdrawal confirmation and success status.

    Raises:
        HTTPException: 404 if consent not found, 400 if withdrawal fails.

    Example:
        >>> response = await withdraw_consent(
        ...     ProcessingPurpose.ANALYTICS
        ... )
        >>> print(response["message"])
        "Consent withdrawn successfully"
    """
    try:
        success = await gdpr_service.withdraw_consent(
            user_id=str(current_user.id),
            purpose=purpose,
            ip_address="127.0.0.1",  # Would get from request
            user_agent="API"  # Would get from request
        )
        
        if success:
            return {"status": "success", "message": "Consent withdrawn successfully"}
        else:
            raise HTTPException(status_code=404, detail="Consent not found")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/gdpr/data-subject-request")
async def submit_data_subject_request(
    request_data: DataSubjectRequestModel,
    current_user: User = Depends(get_current_user),
    gdpr_service: GDPRComplianceService = Depends(get_gdpr_service)
):
    """Submit a data subject request under GDPR Articles 15-22.

    This endpoint allows data subjects to exercise their rights under GDPR,
    including access, rectification, erasure, and data portability requests.

    Args:
        request_data: Data subject request details and verification method.
        current_user: Authenticated user submitting the request.
        gdpr_service: GDPR compliance service instance.

    Returns:
        Dict containing request ID and submission confirmation.

    Raises:
        HTTPException: 400 if request submission fails due to validation errors.

    Example:
        >>> response = await submit_data_subject_request(
        ...     DataSubjectRequestModel(
        ...         request_type=DataSubjectRightType.ACCESS,
        ...         description="Request copy of all personal data",
        ...         verification_method="email"
        ...     )
        ... )
        >>> print(response["request_id"])
    """
    try:
        request = await gdpr_service.submit_data_subject_request(
            user_id=str(current_user.id),
            request_type=request_data.request_type,
            description=request_data.description,
            verification_method=request_data.verification_method
        )
        
        return {
            "status": "success",
            "request_id": request.request_id,
            "message": "Data subject request submitted successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/gdpr/data-subject-request/{request_id}")
async def get_data_subject_request(
    request_id: str,
    current_user: User = Depends(get_current_user),
    gdpr_service: GDPRComplianceService = Depends(get_gdpr_service)
):
    """Get status of a data subject request."""
    if request_id not in gdpr_service.data_subject_requests:
        raise HTTPException(status_code=404, detail="Request not found")
    
    request = gdpr_service.data_subject_requests[request_id]
    
    # Check if user owns the request or has admin permissions
    if request.user_id != str(current_user.id):
        require_permission(Permission.ADMIN_USERS)(current_user)
    
    return {
        "request_id": request.request_id,
        "request_type": request.request_type.value,
        "status": request.status,
        "submitted_at": request.submitted_at.isoformat(),
        "completed_at": request.completed_at.isoformat() if request.completed_at else None,
        "description": request.description
    }


@router.post("/gdpr/data-breach")
@require_permission(Permission.ADMIN_SYSTEM)
async def report_data_breach(
    breach_data: DataBreachReport,
    current_user: User = Depends(get_current_user),
    gdpr_service: GDPRComplianceService = Depends(get_gdpr_service)
):
    """Report a data breach for GDPR compliance."""
    try:
        breach = await gdpr_service.report_data_breach(
            description=breach_data.description,
            affected_data_subjects=breach_data.affected_data_subjects,
            data_categories_affected=breach_data.data_categories_affected,
            likely_consequences=breach_data.likely_consequences,
            measures_taken=breach_data.measures_taken,
            risk_level=breach_data.risk_level
        )
        
        return {
            "status": "success",
            "breach_id": breach.breach_id,
            "notification_required": breach.notification_required,
            "message": "Data breach reported successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/gdpr/processing-record")
@require_permission(Permission.ADMIN_SYSTEM)
async def get_processing_record(
    gdpr_service: GDPRComplianceService = Depends(get_gdpr_service)
):
    """Get Article 30 record of processing activities."""
    try:
        record = await gdpr_service.generate_processing_record()
        return record
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Audit and Logging Endpoints
@router.get("/audit/events")
@require_permission(Permission.VIEW_AUDIT_LOGS)
async def get_audit_events(
    resource_type: Optional[str] = Query(None),
    resource_id: Optional[str] = Query(None),
    user_id: Optional[str] = Query(None),
    event_types: Optional[List[str]] = Query(None),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    limit: int = Query(100, le=1000),
    audit_service: EnhancedAuditService = Depends(get_audit_service)
):
    """Get audit events with comprehensive filtering options.

    This endpoint provides access to audit events with extensive filtering
    capabilities for compliance reporting and security monitoring.

    Args:
        resource_type: Optional filter by resource type.
        resource_id: Optional filter by specific resource ID.
        user_id: Optional filter by user ID.
        event_types: Optional list of event types to filter by.
        start_time: Optional start time for date range filtering.
        end_time: Optional end time for date range filtering.
        limit: Maximum number of events to return (1-1000).
        audit_service: Enhanced audit service instance.

    Returns:
        Dict containing filtered audit events and total count.

    Raises:
        HTTPException: 500 if audit retrieval fails due to system error.

    Example:
        >>> response = await get_audit_events(
        ...     resource_type="user",
        ...     event_types=["LOGIN", "LOGOUT"],
        ...     limit=50
        ... )
        >>> print(f"Found {response['total']} events")
    """
    try:
        # Convert string event types to enum
        event_type_enums = None
        if event_types:
            event_type_enums = [AuditEventType(et) for et in event_types]
        
        events = await audit_service.get_audit_trail(
            resource_type=resource_type,
            resource_id=resource_id,
            user_id=user_id,
            event_types=event_type_enums,
            start_time=start_time,
            end_time=end_time,
            limit=limit
        )
        
        return {
            "events": [
                {
                    "event_id": event.event_id,
                    "timestamp": event.timestamp.isoformat(),
                    "event_type": event.event_type.value,
                    "severity": event.severity.value,
                    "user_id": event.user_id,
                    "action": event.action,
                    "description": event.description,
                    "resource_type": event.resource_type,
                    "resource_id": event.resource_id
                }
                for event in events
            ],
            "total": len(events)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/audit/integrity")
@require_permission(Permission.ADMIN_SYSTEM)
async def verify_audit_integrity(
    audit_service: EnhancedAuditService = Depends(get_audit_service)
):
    """Verify audit log integrity."""
    try:
        verification_results = await audit_service.verify_audit_integrity()
        return verification_results
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/audit/compliance-report")
@require_permission(Permission.VIEW_AUDIT_LOGS)
async def generate_compliance_report(
    report_request: ComplianceReportRequest,
    audit_service: EnhancedAuditService = Depends(get_audit_service)
):
    """Generate compliance report for specific framework."""
    try:
        report = await audit_service.generate_compliance_report(
            framework=report_request.framework,
            start_time=report_request.start_date,
            end_time=report_request.end_date
        )
        return report
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Least Privilege Access Management Endpoints
@router.post("/access/request")
async def request_access(
    access_request: AccessRequestModel,
    current_user: User = Depends(get_current_user),
    privilege_service: LeastPrivilegeService = Depends(get_privilege_service)
):
    """Request access to a resource."""
    try:
        request = await privilege_service.request_access(
            user_id=str(current_user.id),
            resource_type=access_request.resource_type,
            access_types=access_request.access_types,
            justification=access_request.justification,
            business_justification=access_request.business_justification,
            requested_by=str(current_user.id),
            resource_id=access_request.resource_id,
            duration_hours=access_request.duration_hours,
            emergency=access_request.emergency
        )
        
        return {
            "status": "success",
            "request_id": request.request_id,
            "approval_required": request.status.value == "pending",
            "message": "Access request submitted successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/access/approve/{request_id}")
@require_permission(Permission.ADMIN_USERS)
async def approve_access_request(
    request_id: str,
    comments: Optional[str] = Body(None),
    current_user: User = Depends(get_current_user),
    privilege_service: LeastPrivilegeService = Depends(get_privilege_service)
):
    """Approve an access request."""
    try:
        success = await privilege_service.approve_access_request(
            request_id=request_id,
            approved_by=str(current_user.id),
            comments=comments
        )
        
        if success:
            return {"status": "success", "message": "Access request approved"}
        else:
            raise HTTPException(status_code=400, detail="Failed to approve request")
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/access/revoke")
@require_permission(Permission.ADMIN_USERS)
async def revoke_access(
    user_id: str,
    resource_type: str,
    resource_id: Optional[str] = Query(None),
    reason: str = Query("Manual revocation"),
    current_user: User = Depends(get_current_user),
    privilege_service: LeastPrivilegeService = Depends(get_privilege_service)
):
    """Revoke access to a resource."""
    try:
        success = await privilege_service.revoke_access(
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            revoked_by=str(current_user.id),
            reason=reason
        )
        
        if success:
            return {"status": "success", "message": "Access revoked successfully"}
        else:
            raise HTTPException(status_code=404, detail="No access found to revoke")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/access/escalate")
@require_permission(Permission.ADMIN_USERS)
async def escalate_privileges(
    escalation_request: PrivilegeEscalationRequest,
    target_user_id: str,
    current_user: User = Depends(get_current_user),
    privilege_service: LeastPrivilegeService = Depends(get_privilege_service)
):
    """Escalate user privileges temporarily."""
    try:
        escalation = await privilege_service.escalate_privileges(
            user_id=target_user_id,
            target_role=escalation_request.target_role,
            justification=escalation_request.justification,
            approved_by=str(current_user.id),
            duration_hours=escalation_request.duration_hours,
            emergency=escalation_request.emergency
        )
        
        return {
            "status": "success",
            "escalation_id": escalation.escalation_id,
            "expires_at": escalation.expires_at.isoformat() if escalation.expires_at else None,
            "message": "Privileges escalated successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/access/cleanup")
@require_permission(Permission.ADMIN_SYSTEM)
async def cleanup_expired_access(
    privilege_service: LeastPrivilegeService = Depends(get_privilege_service)
):
    """Clean up expired access grants and escalations."""
    try:
        cleanup_stats = await privilege_service.cleanup_expired_access()
        return {
            "status": "success",
            "cleanup_stats": cleanup_stats,
            "message": "Access cleanup completed"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# Compliance Dashboard Endpoints
@router.get("/dashboard/overview")
@require_permission(Permission.VIEW_AUDIT_LOGS)
async def get_compliance_overview(
    gdpr_service: GDPRComplianceService = Depends(get_gdpr_service),
    audit_service: EnhancedAuditService = Depends(get_audit_service),
    privilege_service: LeastPrivilegeService = Depends(get_privilege_service)
):
    """Get compliance dashboard overview."""
    try:
        # Get recent activity counts
        recent_events = await audit_service.get_audit_trail(limit=1000)
        
        # Calculate metrics
        overview = {
            "gdpr": {
                "active_consents": len([c for c in gdpr_service.consent_records.values() if c.status.value == "given"]),
                "pending_requests": len([r for r in gdpr_service.data_subject_requests.values() if r.status == "pending"]),
                "data_breaches": len(gdpr_service.data_breaches)
            },
            "audit": {
                "total_events_30d": len([e for e in recent_events if e.timestamp > datetime.utcnow() - timedelta(days=30)]),
                "critical_events_7d": len([e for e in recent_events if e.severity.value == "critical" and e.timestamp > datetime.utcnow() - timedelta(days=7)]),
                "integrity_status": "verified"  # Would call verify_audit_integrity()
            },
            "access": {
                "pending_requests": len([r for r in privilege_service.access_requests.values() if r.status.value == "pending"]),
                "active_escalations": len([e for e in privilege_service.privilege_escalations.values() if not e.reverted_at]),
                "overdue_reviews": len([r for r in privilege_service.access_reviews.values() if r.status == "overdue"])
            },
            "last_updated": datetime.utcnow().isoformat()
        }
        
        return overview
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
