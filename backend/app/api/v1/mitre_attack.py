"""MITRE ATT&CK Framework API Endpoints.

This module provides comprehensive REST API endpoints for MITRE ATT&CK framework
integration within the Blast-Radius Security Tool, enabling advanced threat
intelligence and attack pattern analysis.

The module supports:
- Technique and tactic management across all ATT&CK domains
- Real-time event correlation with ATT&CK techniques
- Attack pattern identification and analysis
- Threat actor group attribution and profiling
- IOC enrichment with ATT&CK context and intelligence
- ATT&CK Navigator layer generation for visualization
- Comprehensive statistics and reporting

Example:
    Basic technique search:
        GET /api/v1/mitre-attack/techniques?search=lateral+movement

    Event correlation:
        POST /api/v1/mitre-attack/correlate
        {
            "event_data": {
                "process_name": "powershell.exe",
                "command_line": "Invoke-Expression",
                "source_ip": "*************"
            }
        }

Attributes:
    router (APIRouter): FastAPI router for MITRE ATT&CK endpoints.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api.deps import get_db, get_current_active_user
from app.db.models.user import User
from app.services.mitre_attack_service import (
    MitreAttackService,
    AttackTechnique,
    AttackGroup,
    AttackSoftware,
    TechniqueCorrelation,
    AttackPattern,
    AttackDomain,
    ConfidenceLevel
)

router = APIRouter()


# Pydantic models for API
class TechniqueResponse(BaseModel):
    """Response model for MITRE ATT&CK technique data.

    This model represents a complete MITRE ATT&CK technique with all
    associated metadata, relationships, and defensive information.

    Attributes:
        technique_id: MITRE ATT&CK technique identifier (e.g., "T1055").
        name: Human-readable name of the technique.
        description: Detailed description of the technique.
        tactic: Primary tactic this technique belongs to.
        domain: ATT&CK domain (enterprise, mobile, ics).
        platforms: List of platforms where technique applies.
        data_sources: List of data sources for detection.
        mitigations: List of mitigation strategies.
        detection_methods: List of detection approaches.
        sub_techniques: List of sub-technique IDs.
        kill_chain_phases: List of kill chain phases.
        created: Timestamp when technique was created.
        modified: Timestamp when technique was last modified.
        version: Version of the technique definition.
        is_sub_technique: Whether this is a sub-technique.
        parent_technique_id: Parent technique ID if this is a sub-technique.
    """
    technique_id: str
    name: str
    description: str
    tactic: str
    domain: str
    platforms: List[str]
    data_sources: List[str]
    mitigations: List[str]
    detection_methods: List[str]
    sub_techniques: List[str]
    kill_chain_phases: List[str]
    created: datetime
    modified: datetime
    version: str
    is_sub_technique: bool
    parent_technique_id: Optional[str] = None

    @classmethod
    def from_technique(cls, technique: AttackTechnique) -> "TechniqueResponse":
        """Create response model from AttackTechnique domain object.

        Args:
            technique: AttackTechnique domain object to convert.

        Returns:
            TechniqueResponse with all fields populated from domain object.
        """
        return cls(
            technique_id=technique.technique_id,
            name=technique.name,
            description=technique.description,
            tactic=technique.tactic,
            domain=technique.domain.value,
            platforms=technique.platforms,
            data_sources=technique.data_sources,
            mitigations=technique.mitigations,
            detection_methods=technique.detection_methods,
            sub_techniques=technique.sub_techniques,
            kill_chain_phases=technique.kill_chain_phases,
            created=technique.created,
            modified=technique.modified,
            version=technique.version,
            is_sub_technique=technique.is_sub_technique,
            parent_technique_id=technique.parent_technique_id
        )


class GroupResponse(BaseModel):
    """Response model for MITRE ATT&CK threat actor group data.

    This model represents a threat actor group with all associated
    techniques, software, and campaign information.

    Attributes:
        group_id: MITRE ATT&CK group identifier (e.g., "G0016").
        name: Human-readable name of the threat actor group.
        description: Detailed description of the group's activities.
        aliases: List of alternative names for the group.
        techniques: List of technique IDs used by this group.
        software: List of software IDs used by this group.
        associated_campaigns: List of campaigns attributed to this group.
        created: Timestamp when group was first documented.
        modified: Timestamp when group information was last updated.
        version: Version of the group definition.
    """
    group_id: str
    name: str
    description: str
    aliases: List[str]
    techniques: List[str]
    software: List[str]
    associated_campaigns: List[str]
    created: datetime
    modified: datetime
    version: str

    @classmethod
    def from_group(cls, group: AttackGroup) -> "GroupResponse":
        """Create response model from AttackGroup domain object.

        Args:
            group: AttackGroup domain object to convert.

        Returns:
            GroupResponse with all fields populated from domain object.
        """
        return cls(
            group_id=group.group_id,
            name=group.name,
            description=group.description,
            aliases=group.aliases,
            techniques=group.techniques,
            software=group.software,
            associated_campaigns=group.associated_campaigns,
            created=group.created,
            modified=group.modified,
            version=group.version
        )


class SoftwareResponse(BaseModel):
    """Response model for MITRE ATT&CK software/malware data.

    This model represents software or malware documented in the MITRE ATT&CK
    framework with all associated techniques and platform information.

    Attributes:
        software_id: MITRE ATT&CK software identifier (e.g., "S0154").
        name: Human-readable name of the software/malware.
        description: Detailed description of the software capabilities.
        software_type: Type of software (malware, tool, etc.).
        aliases: List of alternative names for the software.
        techniques: List of technique IDs used by this software.
        platforms: List of platforms where software operates.
        created: Timestamp when software was first documented.
        modified: Timestamp when software information was last updated.
        version: Version of the software definition.
    """
    software_id: str
    name: str
    description: str
    software_type: str
    aliases: List[str]
    techniques: List[str]
    platforms: List[str]
    created: datetime
    modified: datetime
    version: str

    @classmethod
    def from_software(cls, software: AttackSoftware) -> "SoftwareResponse":
        """Create response model from AttackSoftware domain object.

        Args:
            software: AttackSoftware domain object to convert.

        Returns:
            SoftwareResponse with all fields populated from domain object.
        """
        return cls(
            software_id=software.software_id,
            name=software.name,
            description=software.description,
            software_type=software.software_type,
            aliases=software.aliases,
            techniques=software.techniques,
            platforms=software.platforms,
            created=software.created,
            modified=software.modified,
            version=software.version
        )


class EventCorrelationRequest(BaseModel):
    """Request model for security event correlation with MITRE ATT&CK.

    This model defines the structure for submitting security events
    for correlation against MITRE ATT&CK techniques.

    Attributes:
        event_data: Dictionary containing security event data including
            fields like process_name, command_line, network_connections,
            file_operations, registry_changes, etc.

    Example:
        >>> request = EventCorrelationRequest(
        ...     event_data={
        ...         "process_name": "powershell.exe",
        ...         "command_line": "Invoke-Expression (New-Object Net.WebClient)",
        ...         "parent_process": "winword.exe",
        ...         "network_connections": ["*************:443"]
        ...     }
        ... )
    """
    event_data: Dict[str, Any] = Field(..., description="Security event data")


class CorrelationResponse(BaseModel):
    """Response model for security event correlation results.

    This model represents the result of correlating a security event
    with MITRE ATT&CK techniques, including confidence metrics and evidence.

    Attributes:
        correlation_id: Unique identifier for the correlation result.
        event_id: ID of the security event that was correlated.
        technique_id: MITRE ATT&CK technique ID that was matched.
        technique_name: Human-readable name of the matched technique.
        confidence: Confidence level (HIGH, MEDIUM, LOW).
        confidence_score: Numerical confidence score (0-1).
        evidence: List of evidence items supporting the correlation.
        context: Additional context data for the correlation.
        timestamp: When the correlation was performed.
        analyst_verified: Whether an analyst has verified this correlation.
        false_positive: Whether this correlation is marked as false positive.
    """
    correlation_id: str
    event_id: str
    technique_id: str
    technique_name: str
    confidence: str
    confidence_score: float
    evidence: List[str]
    context: Dict[str, Any]
    timestamp: datetime
    analyst_verified: bool
    false_positive: bool

    @classmethod
    def from_correlation(cls, correlation: TechniqueCorrelation, technique_name: str = "") -> "CorrelationResponse":
        """Create response model from TechniqueCorrelation domain object.

        Args:
            correlation: TechniqueCorrelation domain object to convert.
            technique_name: Human-readable technique name.

        Returns:
            CorrelationResponse with all fields populated from domain object.
        """
        return cls(
            correlation_id=correlation.correlation_id,
            event_id=correlation.event_id,
            technique_id=correlation.technique_id,
            technique_name=technique_name,
            confidence=correlation.confidence.value,
            confidence_score=correlation.confidence_score,
            evidence=correlation.evidence,
            context=correlation.context,
            timestamp=correlation.timestamp,
            analyst_verified=correlation.analyst_verified,
            false_positive=correlation.false_positive
        )


class PatternResponse(BaseModel):
    pattern_id: str
    name: str
    techniques: List[str]
    sequence: List[str]
    confidence: float
    first_seen: datetime
    last_seen: datetime
    event_count: int
    affected_assets_count: int
    attributed_groups: List[str]
    campaign_id: Optional[str] = None

    @classmethod
    def from_pattern(cls, pattern: AttackPattern) -> "PatternResponse":
        return cls(
            pattern_id=pattern.pattern_id,
            name=pattern.name,
            techniques=pattern.techniques,
            sequence=pattern.sequence,
            confidence=pattern.confidence,
            first_seen=pattern.first_seen,
            last_seen=pattern.last_seen,
            event_count=pattern.event_count,
            affected_assets_count=len(pattern.affected_assets),
            attributed_groups=pattern.attributed_groups,
            campaign_id=pattern.campaign_id
        )


class IOCEnrichmentRequest(BaseModel):
    ioc: str = Field(..., description="Indicator of Compromise")
    ioc_type: str = Field(..., description="Type of IOC (ip, domain, hash, etc.)")


class NavigatorLayerRequest(BaseModel):
    techniques: List[str] = Field(..., description="List of technique IDs")
    name: str = Field("Custom Layer", description="Layer name")
    description: str = Field("", description="Layer description")


# Dependency to get MITRE ATT&CK service
def get_mitre_service(db: Session = Depends(get_db)) -> MitreAttackService:
    """Get MITRE ATT&CK service instance.

    This dependency function provides a MitreAttackService instance
    initialized with the current database session for ATT&CK operations.

    Args:
        db: Database session dependency.

    Returns:
        MitreAttackService: Service instance for ATT&CK operations.
    """
    return MitreAttackService(db)


@router.get("/techniques", response_model=List[TechniqueResponse])
async def list_techniques(
    domain: Optional[str] = Query(None, description="Filter by domain (enterprise, mobile, ics)"),
    tactic: Optional[str] = Query(None, description="Filter by tactic"),
    search: Optional[str] = Query(None, description="Search query"),
    limit: int = Query(100, le=1000, description="Maximum number of results"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """List MITRE ATT&CK techniques with optional filtering.

    This endpoint provides comprehensive access to the MITRE ATT&CK technique
    database with support for domain filtering, tactic filtering, and text search.

    Args:
        domain: Optional domain filter (enterprise, mobile, ics).
        tactic: Optional tactic filter (e.g., "initial-access").
        search: Optional text search query for technique names/descriptions.
        limit: Maximum number of results to return (1-1000).
        db: Database session for data access.
        current_user: Authenticated user making the request.
        mitre_service: MITRE ATT&CK service instance.

    Returns:
        List[TechniqueResponse]: List of techniques matching the criteria.

    Raises:
        HTTPException: 400 if invalid domain specified,
            500 if retrieval fails due to system error.

    Example:
        >>> # Search for lateral movement techniques
        >>> response = await list_techniques(
        ...     search="lateral movement",
        ...     domain="enterprise",
        ...     limit=50
        ... )
        >>> print(f"Found {len(response)} techniques")
    """
    try:
        if search:
            # Search techniques
            attack_domain = AttackDomain(domain) if domain else None
            techniques = mitre_service.search_techniques(search, attack_domain)
        elif tactic:
            # Filter by tactic
            attack_domain = AttackDomain(domain) if domain else None
            techniques = mitre_service.get_techniques_by_tactic(tactic, attack_domain)
        else:
            # Get all techniques
            techniques = list(mitre_service.techniques.values())
            if domain:
                attack_domain = AttackDomain(domain)
                techniques = [t for t in techniques if t.domain == attack_domain]
        
        # Apply limit
        techniques = techniques[:limit]
        
        return [TechniqueResponse.from_technique(t) for t in techniques]
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid domain: {domain}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve techniques: {str(e)}"
        )


@router.get("/techniques/{technique_id}", response_model=TechniqueResponse)
async def get_technique(
    technique_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Get specific MITRE ATT&CK technique by ID.

    This endpoint retrieves detailed information about a specific
    MITRE ATT&CK technique including all metadata and relationships.

    Args:
        technique_id: MITRE ATT&CK technique identifier (e.g., "T1055").
        db: Database session for data access.
        current_user: Authenticated user making the request.
        mitre_service: MITRE ATT&CK service instance.

    Returns:
        TechniqueResponse: Complete technique information.

    Raises:
        HTTPException: 404 if technique not found,
            500 if retrieval fails due to system error.

    Example:
        >>> response = await get_technique("T1055")
        >>> print(f"Technique: {response.name}")
    """
    try:
        technique = mitre_service.get_technique(technique_id)
        if not technique:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Technique {technique_id} not found"
            )
        return TechniqueResponse.from_technique(technique)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve technique: {str(e)}"
        )


@router.get("/groups", response_model=List[GroupResponse])
async def list_groups(
    search: Optional[str] = Query(None, description="Search query"),
    limit: int = Query(100, le=1000, description="Maximum number of results"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """List MITRE ATT&CK threat actor groups."""
    try:
        if search:
            groups = mitre_service.search_groups(search)
        else:
            groups = list(mitre_service.groups.values())
        
        # Apply limit
        groups = groups[:limit]
        
        return [GroupResponse.from_group(g) for g in groups]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve groups: {str(e)}"
        )


@router.get("/groups/{group_id}", response_model=GroupResponse)
async def get_group(
    group_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Get specific MITRE ATT&CK threat actor group."""
    try:
        group = mitre_service.get_group(group_id)
        if not group:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Group {group_id} not found"
            )
        return GroupResponse.from_group(group)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve group: {str(e)}"
        )


@router.get("/groups/{group_id}/techniques", response_model=List[TechniqueResponse])
async def get_group_techniques(
    group_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Get techniques used by a specific threat actor group."""
    try:
        techniques = mitre_service.get_group_techniques(group_id)
        if not techniques and group_id not in mitre_service.groups:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Group {group_id} not found"
            )
        return [TechniqueResponse.from_technique(t) for t in techniques]
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve group techniques: {str(e)}"
        )


@router.post("/correlate", response_model=List[CorrelationResponse])
async def correlate_event(
    request: EventCorrelationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Correlate security event to MITRE ATT&CK techniques.

    This endpoint analyzes security event data and correlates it with
    MITRE ATT&CK techniques to provide threat intelligence context.

    Args:
        request: Event correlation request containing security event data.
        background_tasks: FastAPI background tasks for async processing.
        db: Database session for data access.
        current_user: Authenticated user making the request.
        mitre_service: MITRE ATT&CK service instance.

    Returns:
        List[CorrelationResponse]: List of technique correlations with
            confidence scores and supporting evidence.

    Raises:
        HTTPException: 500 if correlation fails due to system error.

    Example:
        >>> response = await correlate_event(
        ...     EventCorrelationRequest(
        ...         event_data={
        ...             "process_name": "powershell.exe",
        ...             "command_line": "Invoke-Expression"
        ...         }
        ...     )
        ... )
        >>> print(f"Found {len(response)} technique correlations")
    """
    try:
        correlations = await mitre_service.correlate_event_to_techniques(request.event_data)
        
        # Convert to response format
        responses = []
        for correlation in correlations:
            technique = mitre_service.get_technique(correlation.technique_id)
            technique_name = technique.name if technique else correlation.technique_id
            responses.append(CorrelationResponse.from_correlation(correlation, technique_name))
        
        return responses
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to correlate event: {str(e)}"
        )


@router.get("/patterns", response_model=List[PatternResponse])
async def list_attack_patterns(
    time_window_hours: int = Query(24, ge=1, le=168, description="Time window in hours"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """List identified attack patterns."""
    try:
        patterns = await mitre_service.identify_attack_patterns(time_window_hours)
        return [PatternResponse.from_pattern(p) for p in patterns]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve attack patterns: {str(e)}"
        )


@router.post("/enrich-ioc")
async def enrich_ioc(
    request: IOCEnrichmentRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Enrich IOC with MITRE ATT&CK context."""
    try:
        enrichment = await mitre_service.enrich_ioc_with_attack_context(
            request.ioc, request.ioc_type
        )
        return enrichment
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to enrich IOC: {str(e)}"
        )


@router.post("/navigator-layer")
async def generate_navigator_layer(
    request: NavigatorLayerRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Generate ATT&CK Navigator layer JSON."""
    try:
        layer = mitre_service.generate_attack_navigator_layer(
            techniques=request.techniques,
            name=request.name,
            description=request.description
        )
        return layer
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate navigator layer: {str(e)}"
        )


@router.get("/statistics")
async def get_statistics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Get MITRE ATT&CK data statistics."""
    try:
        stats = mitre_service.get_statistics()
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve statistics: {str(e)}"
        )


@router.post("/update")
async def update_attack_data(
    domain: Optional[str] = Query(None, description="Domain to update (enterprise, mobile, ics)"),
    force: bool = Query(False, description="Force update even if data is current"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    mitre_service: MitreAttackService = Depends(get_mitre_service)
):
    """Update MITRE ATT&CK data."""
    try:
        if domain:
            attack_domain = AttackDomain(domain)
            background_tasks.add_task(
                mitre_service.load_attack_data, attack_domain, force
            )
            return {"message": f"Started update for {domain} domain"}
        else:
            background_tasks.add_task(mitre_service.update_all_domains)
            return {"message": "Started update for all domains"}
            
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid domain: {domain}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start update: {str(e)}"
        )
