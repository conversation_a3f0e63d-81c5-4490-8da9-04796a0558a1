"""Threat Modeling API Endpoints.

This module provides comprehensive REST API endpoints for advanced threat modeling,
quantitative risk assessment, and attack simulation capabilities within the
Blast-Radius Security Tool.

The module supports:
- Threat actor profiling and intelligence management
- Quantitative risk assessment using industry-standard methodologies
- Attack scenario simulation and impact analysis
- Risk tolerance evaluation and compliance reporting
- Threat model export and visualization capabilities

Example:
    Basic threat actor listing:
        GET /api/v1/threat-modeling/threat-actors

    Quantitative risk assessment:
        POST /api/v1/threat-modeling/risk-assessment
        {
            "asset_id": "critical-database-01"
        }

    Attack simulation:
        POST /api/v1/threat-modeling/simulate-attack
        {
            "threat_actor_id": "apt29",
            "target_assets": ["web-server", "database"],
            "scenario_name": "Advanced Persistent Threat"
        }

Attributes:
    router (APIRouter): FastAPI router for threat modeling endpoints.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.api.deps import get_db, get_current_active_user
from app.db.models.user import User
from app.services.threat_modeling_service import (
    ThreatModelingService,
    ThreatActorProfile,
    ThreatActorType,
    AttackMotivation,
    QuantitativeRiskAssessment,
    AttackSimulationResult
)

router = APIRouter()


# Pydantic models for API
class ThreatActorResponse(BaseModel):
    """Response model for threat actor profile data.

    This model represents a comprehensive threat actor profile with
    all associated capabilities, motivations, and intelligence metrics.

    Attributes:
        actor_id: Unique identifier for the threat actor.
        name: Human-readable name of the threat actor or group.
        actor_type: Type of threat actor (nation-state, cybercriminal, etc.).
        sophistication_level: Technical sophistication level (0-1).
        motivation: Primary motivation for attacks.
        target_sectors: List of industry sectors typically targeted.
        resource_level: Available resources and funding level (0-1).
        stealth_capability: Ability to remain undetected (0-1).
        persistence_level: Ability to maintain long-term access (0-1).
        threat_score: Overall threat score based on capabilities (0-100).
        attribution_confidence: Confidence in attribution (0-1).
        last_activity: Timestamp of last observed activity.
        geographic_origin: Optional geographic origin or base of operations.
    """
    actor_id: str
    name: str
    actor_type: str
    sophistication_level: float
    motivation: str
    target_sectors: List[str]
    resource_level: float
    stealth_capability: float
    persistence_level: float
    threat_score: float
    attribution_confidence: float
    last_activity: datetime
    geographic_origin: Optional[str] = None

    @classmethod
    def from_threat_actor(cls, actor: ThreatActorProfile) -> "ThreatActorResponse":
        """Create response model from ThreatActorProfile domain object.

        Args:
            actor: ThreatActorProfile domain object to convert.

        Returns:
            ThreatActorResponse with all fields populated from domain object.
        """
        return cls(
            actor_id=actor.actor_id,
            name=actor.name,
            actor_type=actor.actor_type.value,
            sophistication_level=actor.sophistication_level,
            motivation=actor.motivation.value,
            target_sectors=actor.target_sectors,
            resource_level=actor.resource_level,
            stealth_capability=actor.stealth_capability,
            persistence_level=actor.persistence_level,
            threat_score=actor.threat_score,
            attribution_confidence=actor.attribution_confidence,
            last_activity=actor.last_activity,
            geographic_origin=actor.geographic_origin
        )


class RiskAssessmentRequest(BaseModel):
    """Request model for quantitative risk assessment.

    This model defines the parameters for performing comprehensive
    quantitative risk assessment on a specific asset.

    Attributes:
        asset_id: Unique identifier of the asset to assess.

    Example:
        >>> request = RiskAssessmentRequest(
        ...     asset_id="critical-database-01"
        ... )
    """
    asset_id: str = Field(..., description="Asset ID for risk assessment")


class RiskAssessmentResponse(BaseModel):
    """Response model for quantitative risk assessment results.

    This model represents comprehensive quantitative risk assessment
    results using industry-standard methodologies like ALE calculation.

    Attributes:
        asset_id: Unique identifier of the assessed asset.
        annual_loss_expectancy: Expected annual loss in dollars (ALE).
        single_loss_expectancy: Expected loss per incident (SLE).
        annual_rate_of_occurrence: Expected incidents per year (ARO).
        exposure_factor: Percentage of asset value at risk (0-1).
        asset_value: Total value of the asset in dollars.
        threat_frequency: Frequency of threat occurrence (0-1).
        vulnerability_score: Vulnerability assessment score (0-1).
        control_effectiveness: Effectiveness of security controls (0-1).
        residual_risk: Remaining risk after controls (0-100).
        risk_tolerance: Acceptable risk threshold (0-100).
        risk_level: Human-readable risk level (Low/Medium/High/Critical).
        exceeds_tolerance: Whether risk exceeds acceptable tolerance.
    """
    asset_id: str
    annual_loss_expectancy: float
    single_loss_expectancy: float
    annual_rate_of_occurrence: float
    exposure_factor: float
    asset_value: float
    threat_frequency: float
    vulnerability_score: float
    control_effectiveness: float
    residual_risk: float
    risk_tolerance: float
    risk_level: str
    exceeds_tolerance: bool

    @classmethod
    def from_assessment(cls, assessment: QuantitativeRiskAssessment) -> "RiskAssessmentResponse":
        """Create response model from QuantitativeRiskAssessment domain object.

        Args:
            assessment: QuantitativeRiskAssessment domain object to convert.

        Returns:
            RiskAssessmentResponse with all fields populated from domain object.
        """
        return cls(
            asset_id=assessment.asset_id,
            annual_loss_expectancy=assessment.annual_loss_expectancy,
            single_loss_expectancy=assessment.single_loss_expectancy,
            annual_rate_of_occurrence=assessment.annual_rate_of_occurrence,
            exposure_factor=assessment.exposure_factor,
            asset_value=assessment.asset_value,
            threat_frequency=assessment.threat_frequency,
            vulnerability_score=assessment.vulnerability_score,
            control_effectiveness=assessment.control_effectiveness,
            residual_risk=assessment.residual_risk,
            risk_tolerance=assessment.risk_tolerance,
            risk_level=assessment.risk_level,
            exceeds_tolerance=assessment.exceeds_tolerance
        )


class AttackSimulationRequest(BaseModel):
    threat_actor_id: str = Field(..., description="Threat actor ID for simulation")
    target_assets: List[str] = Field(..., description="Target asset IDs")
    scenario_name: Optional[str] = Field(None, description="Custom scenario name")


class AttackSimulationResponse(BaseModel):
    simulation_id: str
    scenario_name: str
    threat_actor_id: str
    threat_actor_name: str
    success_probability: float
    detection_probability: float
    time_to_compromise: float
    time_to_detection: float
    impact_assessment: Dict[str, float]
    affected_assets_count: int
    data_exfiltrated: float
    service_downtime: float
    financial_impact: float
    reputation_impact: float
    regulatory_violations: List[str]
    simulation_timestamp: datetime

    @classmethod
    def from_simulation(cls, simulation: AttackSimulationResult) -> "AttackSimulationResponse":
        return cls(
            simulation_id=simulation.simulation_id,
            scenario_name=simulation.scenario_name,
            threat_actor_id=simulation.threat_actor.actor_id,
            threat_actor_name=simulation.threat_actor.name,
            success_probability=simulation.success_probability,
            detection_probability=simulation.detection_probability,
            time_to_compromise=simulation.time_to_compromise,
            time_to_detection=simulation.time_to_detection,
            impact_assessment=simulation.impact_assessment,
            affected_assets_count=len(simulation.affected_assets),
            data_exfiltrated=simulation.data_exfiltrated,
            service_downtime=simulation.service_downtime,
            financial_impact=simulation.financial_impact,
            reputation_impact=simulation.reputation_impact,
            regulatory_violations=simulation.regulatory_violations,
            simulation_timestamp=simulation.simulation_timestamp
        )


# Dependency to get threat modeling service
def get_threat_service(db: Session = Depends(get_db)) -> ThreatModelingService:
    """Get threat modeling service instance."""
    return ThreatModelingService(db)


@router.get("/threat-actors", response_model=List[ThreatActorResponse])
async def list_threat_actors(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """List all available threat actor profiles."""
    try:
        actors = threat_service.list_threat_actors()
        return [ThreatActorResponse.from_threat_actor(actor) for actor in actors]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve threat actors: {str(e)}"
        )


@router.get("/threat-actors/{actor_id}", response_model=ThreatActorResponse)
async def get_threat_actor(
    actor_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """Get specific threat actor profile."""
    try:
        actor = threat_service.get_threat_actor_profile(actor_id)
        if not actor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Threat actor {actor_id} not found"
            )
        return ThreatActorResponse.from_threat_actor(actor)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve threat actor: {str(e)}"
        )


@router.post("/risk-assessment", response_model=RiskAssessmentResponse)
async def perform_risk_assessment(
    request: RiskAssessmentRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """Perform quantitative risk assessment for an asset."""
    try:
        assessment = await threat_service.perform_quantitative_risk_assessment(request.asset_id)
        return RiskAssessmentResponse.from_assessment(assessment)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to perform risk assessment: {str(e)}"
        )


@router.post("/simulate-attack", response_model=AttackSimulationResponse)
async def simulate_attack(
    request: AttackSimulationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """Simulate an attack scenario with a specific threat actor."""
    try:
        simulation = await threat_service.simulate_attack_scenario(
            threat_actor_id=request.threat_actor_id,
            target_assets=request.target_assets,
            scenario_name=request.scenario_name
        )
        return AttackSimulationResponse.from_simulation(simulation)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to simulate attack: {str(e)}"
        )


@router.get("/simulations", response_model=List[AttackSimulationResponse])
async def list_simulations(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """List all cached attack simulation results."""
    try:
        simulations = threat_service.list_simulations()
        return [AttackSimulationResponse.from_simulation(sim) for sim in simulations]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve simulations: {str(e)}"
        )


@router.get("/simulations/{simulation_id}", response_model=AttackSimulationResponse)
async def get_simulation(
    simulation_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """Get specific attack simulation result."""
    try:
        simulation = threat_service.get_simulation_result(simulation_id)
        if not simulation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Simulation {simulation_id} not found"
            )
        return AttackSimulationResponse.from_simulation(simulation)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve simulation: {str(e)}"
        )


@router.get("/export")
async def export_threat_model(
    format: str = Query("json", description="Export format (json)"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """Export threat model data."""
    try:
        export_data = threat_service.export_threat_model(format)
        return {"data": export_data, "format": format, "timestamp": datetime.utcnow()}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export threat model: {str(e)}"
        )


@router.delete("/simulations/cache")
async def clear_simulation_cache(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    threat_service: ThreatModelingService = Depends(get_threat_service)
):
    """Clear simulation cache."""
    try:
        threat_service.clear_simulation_cache()
        return {"message": "Simulation cache cleared successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to clear cache: {str(e)}"
        )
