"""Authentication Service for Login, Logout, and Session Management.

This module provides comprehensive authentication services for the Blast-Radius
Security Tool, including user authentication, multi-factor authentication (MFA),
session management, and security controls.

The service supports:
- Username/email and password authentication with secure password verification
- Multi-factor authentication (MFA) with TOTP, SMS, and email verification
- Secure session management with JWT tokens and refresh token rotation
- Account security features including lockout protection and failed login tracking
- Session cleanup and concurrent session limits
- Comprehensive audit logging for all authentication events

Example:
    Basic authentication:
        >>> auth_service = AuthService(db)
        >>> user, result = auth_service.authenticate_user(
        ...     username="<EMAIL>",
        ...     password="secure_password",
        ...     ip_address="*************"
        ... )
        >>> if result["success"]:
        ...     tokens = auth_service.create_tokens(user, result["session"])

    MFA authentication:
        >>> user, result = auth_service.authenticate_user(
        ...     username="<EMAIL>",
        ...     password="secure_password",
        ...     mfa_code="123456"
        ... )

Security Features:
- Secure password hashing and verification
- Account lockout protection against brute force attacks
- MFA support with TOTP, SMS, and backup codes
- Session token rotation and secure session management
- Comprehensive audit logging and monitoring
- IP address and user agent tracking for security analysis

Attributes:
    AuthenticationError: Exception class for authentication-related errors.
    AuthService: Main authentication service class.
"""

from datetime import datetime, timedelta
import logging
import secrets
from typing import Any
import uuid

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session

from app.config import settings
from app.core.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
)
from app.db.models.auth import LoginAttempt, MFADevice
from app.db.models.user import User, UserSession

logger = logging.getLogger(__name__)


class AuthenticationError(Exception):
    """Authentication related errors."""


class AuthService:
    """Service for authentication operations."""

    def __init__(self, db: Session):
        """
        Initialize auth service.

        Args:
            db: Database session
        """
        self.db = db

    def authenticate_user(
        self,
        username: str,
        password: str,
        ip_address: str | None = None,
        user_agent: str | None = None,
        mfa_code: str | None = None,
    ) -> tuple[User | None, dict[str, Any]]:
        """
        Authenticate user with username/password and optional MFA.

        Args:
            username: Username or email
            password: Password
            ip_address: Client IP address
            user_agent: Client user agent
            mfa_code: MFA verification code

        Returns:
            Tuple of (User object if successful, authentication result dict)
        """
        auth_result = {
            "success": False,
            "user": None,
            "requires_mfa": False,
            "mfa_methods": [],
            "error": None,
            "session_id": None,
        }

        try:
            # Find user by username or email
            user = (
                self.db.query(User)
                .filter(
                    or_(
                        User.username == username.lower(),
                        User.email == username.lower(),
                    )
                )
                .filter(User.is_deleted == False)
                .first()
            )

            # Record login attempt
            login_attempt = LoginAttempt.create_attempt(
                username=username,
                success=False,  # Will update if successful
                ip_address=ip_address,
                user_agent=user_agent,
                user_id=user.id if user else None,
            )

            # Check if user exists
            if not user:
                login_attempt.failure_reason = "invalid_username"
                self.db.add(login_attempt)
                self.db.commit()
                auth_result["error"] = "Invalid credentials"
                return None, auth_result

            # Check if account is active
            if not user.is_active:
                login_attempt.failure_reason = "account_inactive"
                self.db.add(login_attempt)
                self.db.commit()
                auth_result["error"] = "Account is inactive"
                return None, auth_result

            # Check if account is locked
            if user.is_account_locked():
                login_attempt.failure_reason = "account_locked"
                self.db.add(login_attempt)
                self.db.commit()
                auth_result["error"] = "Account is locked"
                return None, auth_result

            # Verify password
            if not user.check_password(password):
                user.increment_failed_login()
                login_attempt.failure_reason = "invalid_password"
                self.db.add(login_attempt)
                self.db.commit()
                auth_result["error"] = "Invalid credentials"
                return None, auth_result

            # Check if MFA is required
            if user.mfa_enabled:
                mfa_devices = (
                    self.db.query(MFADevice)
                    .filter(
                        and_(
                            MFADevice.user_id == user.id,
                            MFADevice.is_active == True,
                            MFADevice.is_verified == True,
                        )
                    )
                    .all()
                )

                if mfa_devices:
                    auth_result["requires_mfa"] = True
                    auth_result["mfa_methods"] = [
                        device.device_type for device in mfa_devices
                    ]

                    if not mfa_code:
                        # MFA required but not provided
                        login_attempt.failure_reason = "mfa_required"
                        self.db.add(login_attempt)
                        self.db.commit()
                        auth_result["error"] = "MFA verification required"
                        return None, auth_result

                    # Verify MFA code
                    mfa_valid = self._verify_mfa_code(user.id, mfa_code)
                    if not mfa_valid:
                        login_attempt.failure_reason = "invalid_mfa"
                        self.db.add(login_attempt)
                        self.db.commit()
                        auth_result["error"] = "Invalid MFA code"
                        return None, auth_result

            # Authentication successful
            user.update_last_login(ip_address)
            login_attempt.success = True

            # Create session
            session = self._create_user_session(
                user=user, ip_address=ip_address, user_agent=user_agent
            )

            self.db.add(login_attempt)
            self.db.commit()

            auth_result.update(
                {
                    "success": True,
                    "user": user,
                    "session_id": session.session_token,
                    "requires_mfa": False,
                }
            )

            return user, auth_result

        except Exception as e:
            logger.error(f"Authentication error: {e}")
            self.db.rollback()
            auth_result["error"] = "Authentication failed"
            return None, auth_result

    def _verify_mfa_code(self, user_id: uuid.UUID, code: str) -> bool:
        """
        Verify MFA code for user.

        Args:
            user_id: User ID
            code: MFA code to verify

        Returns:
            True if code is valid, False otherwise
        """
        # Get active MFA devices
        devices = (
            self.db.query(MFADevice)
            .filter(
                and_(
                    MFADevice.user_id == user_id,
                    MFADevice.is_active == True,
                    MFADevice.is_verified == True,
                )
            )
            .all()
        )

        for device in devices:
            if device.device_type == "totp":
                # Verify TOTP code (implementation would use pyotp or similar)
                # For now, simplified verification
                if self._verify_totp_code(device.secret_key, code):
                    device.record_usage()
                    return True
            elif device.device_type in ["sms", "email"]:
                # Verify temporary code
                if device.is_verification_code_valid(code):
                    device.record_usage()
                    return True

            # Check backup codes
            if device.use_backup_code(code):
                return True

        return False

    def _verify_totp_code(self, secret_key: str, code: str) -> bool:
        """
        Verify TOTP code (simplified implementation).

        Args:
            secret_key: TOTP secret key
            code: Code to verify

        Returns:
            True if code is valid, False otherwise
        """
        # In a real implementation, use pyotp or similar library
        # This is a simplified version for demonstration
        import time

        try:
            # Get current time step
            time_step = int(time.time()) // 30

            # Generate TOTP for current and adjacent time steps
            for step in [time_step - 1, time_step, time_step + 1]:
                expected_code = self._generate_totp(secret_key, step)
                if expected_code == code:
                    return True

            return False
        except Exception:
            return False

    def _generate_totp(self, secret_key: str, time_step: int) -> str:
        """
        Generate TOTP code for given time step.

        Args:
            secret_key: TOTP secret key
            time_step: Time step

        Returns:
            6-digit TOTP code
        """
        # Simplified TOTP generation
        key = secret_key.encode()
        msg = struct.pack(">Q", time_step)
        digest = hmac.new(key, msg, hashlib.sha1).digest()
        offset = digest[-1] & 0x0F
        code = struct.unpack(">I", digest[offset : offset + 4])[0] & 0x7FFFFFFF
        return f"{code % 1000000:06d}"

    def _create_user_session(
        self,
        user: User,
        ip_address: str | None = None,
        user_agent: str | None = None,
        remember_me: bool = False,
    ) -> UserSession:
        """
        Create a new user session.

        Args:
            user: User object
            ip_address: Client IP address
            user_agent: Client user agent
            remember_me: Whether to create extended session

        Returns:
            UserSession object
        """
        # Generate session tokens
        session_token = secrets.token_urlsafe(32)
        refresh_token = secrets.token_urlsafe(32)

        # Calculate expiration
        if remember_me:
            expires_in = timedelta(days=30)
        else:
            expires_in = timedelta(minutes=settings.SESSION_TIMEOUT_MINUTES)

        expires_at = datetime.utcnow() + expires_in

        # Create session
        session = UserSession(
            user_id=user.id,
            session_token=session_token,
            refresh_token=refresh_token,
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=expires_at,
        )

        self.db.add(session)

        # Clean up old sessions if user has too many
        self._cleanup_user_sessions(user.id)

        return session

    def _cleanup_user_sessions(self, user_id: uuid.UUID) -> None:
        """
        Clean up old sessions for user.

        Args:
            user_id: User ID
        """
        # Get active sessions count
        active_sessions = (
            self.db.query(UserSession)
            .filter(
                and_(
                    UserSession.user_id == user_id,
                    UserSession.is_active == True,
                    UserSession.expires_at > datetime.utcnow(),
                )
            )
            .count()
        )

        # If too many sessions, deactivate oldest ones
        if active_sessions >= settings.MAX_CONCURRENT_SESSIONS_PER_USER:
            old_sessions = (
                self.db.query(UserSession)
                .filter(
                    and_(UserSession.user_id == user_id, UserSession.is_active == True)
                )
                .order_by(UserSession.last_activity_at.asc())
                .limit(active_sessions - settings.MAX_CONCURRENT_SESSIONS_PER_USER + 1)
                .all()
            )

            for session in old_sessions:
                session.terminate_session("max_sessions_exceeded")

    def create_tokens(self, user: User, session: UserSession) -> dict[str, Any]:
        """
        Create JWT tokens for user.

        Args:
            user: User object
            session: User session

        Returns:
            Dictionary with access and refresh tokens
        """
        # Create access token with user info
        access_token_data = {
            "user_id": str(user.id),
            "username": user.username,
            "session_id": session.session_token,
            "roles": [role.name for role in user.roles if role.is_active],
        }

        access_token = create_access_token(
            subject=access_token_data,
            expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES),
        )

        refresh_token = create_refresh_token(subject=str(user.id))

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        }

    def refresh_access_token(self, refresh_token: str) -> dict[str, Any] | None:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Refresh token

        Returns:
            New token data if successful, None otherwise
        """
        try:
            # Verify refresh token
            user_id = verify_token(refresh_token)
            if not user_id:
                return None

            # Get user and active session
            user = (
                self.db.query(User)
                .filter(
                    and_(
                        User.id == user_id,
                        User.is_active == True,
                        User.is_deleted == False,
                    )
                )
                .first()
            )

            if not user:
                return None

            # Find active session with this refresh token
            session = (
                self.db.query(UserSession)
                .filter(
                    and_(
                        UserSession.user_id == user.id,
                        UserSession.refresh_token == refresh_token,
                        UserSession.is_active == True,
                        UserSession.expires_at > datetime.utcnow(),
                    )
                )
                .first()
            )

            if not session:
                return None

            # Update session activity
            session.extend_session()
            self.db.commit()

            # Create new tokens
            return self.create_tokens(user, session)

        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            return None

    def logout_user(self, session_token: str, all_sessions: bool = False) -> bool:
        """
        Logout user by terminating session(s).

        Args:
            session_token: Session token to logout
            all_sessions: Whether to logout all user sessions

        Returns:
            True if logout successful, False otherwise
        """
        try:
            # Find session
            session = (
                self.db.query(UserSession)
                .filter(UserSession.session_token == session_token)
                .first()
            )

            if not session:
                return False

            if all_sessions:
                # Terminate all user sessions
                user_sessions = (
                    self.db.query(UserSession)
                    .filter(
                        and_(
                            UserSession.user_id == session.user_id,
                            UserSession.is_active == True,
                        )
                    )
                    .all()
                )

                for user_session in user_sessions:
                    user_session.terminate_session("user_logout_all")
            else:
                # Terminate specific session
                session.terminate_session("user_logout")

            self.db.commit()
            return True

        except Exception as e:
            logger.error(f"Logout error: {e}")
            self.db.rollback()
            return False

    def validate_session(self, session_token: str) -> User | None:
        """
        Validate session token and return user.

        Args:
            session_token: Session token to validate

        Returns:
            User object if session is valid, None otherwise
        """
        try:
            # Find active session
            session = (
                self.db.query(UserSession)
                .filter(
                    and_(
                        UserSession.session_token == session_token,
                        UserSession.is_active == True,
                        UserSession.expires_at > datetime.utcnow(),
                    )
                )
                .first()
            )

            if not session:
                return None

            # Get user
            user = (
                self.db.query(User)
                .filter(
                    and_(
                        User.id == session.user_id,
                        User.is_active == True,
                        User.is_deleted == False,
                    )
                )
                .first()
            )

            if not user:
                # User no longer active, terminate session
                session.terminate_session("user_inactive")
                self.db.commit()
                return None

            # Update session activity
            session.last_activity_at = datetime.utcnow()
            self.db.commit()

            return user

        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return None
