"""Asset Management Service Layer.

This module provides comprehensive asset management services for the Blast-Radius
Security Tool, including asset lifecycle management, relationship mapping, discovery
job orchestration, and analytics capabilities.

The service supports:
- Complete asset lifecycle management (CRUD operations)
- Asset relationship management for dependency mapping and attack path analysis
- Asset tagging system for categorization and metadata management
- Discovery job management for automated asset discovery
- Advanced asset search with filtering, sorting, and pagination
- Comprehensive asset statistics and analytics for reporting

Example:
    Basic asset management:
        >>> asset_service = AssetService(db)
        >>> asset = asset_service.create_asset(
        ...     AssetCreate(
        ...         name="Production Database",
        ...         asset_type=AssetType.DATABASE,
        ...         provider=AssetProvider.AWS,
        ...         environment="production"
        ...     )
        ... )

    Asset relationship management:
        >>> relationship = asset_service.create_relationship(
        ...     AssetRelationshipCreate(
        ...         source_asset_id=web_server.id,
        ...         target_asset_id=database.id,
        ...         relationship_type=RelationshipType.CONNECTS_TO,
        ...         protocol="tcp",
        ...         port=5432
        ...     )
        ... )

Service Features:
- Duplicate detection and prevention for assets and relationships
- Configuration change tracking with hash-based detection
- Last-seen timestamp management for asset discovery
- Comprehensive search with multiple filter types
- Asset statistics and analytics for security reporting
- Discovery job lifecycle management with status tracking

Attributes:
    AssetNotFoundError: Exception for missing assets.
    AssetAlreadyExistsError: Exception for duplicate assets.
    RelationshipNotFoundError: Exception for missing relationships.
    DiscoveryJobNotFoundError: Exception for missing discovery jobs.
    AssetService: Main asset management service class.
"""

import hashlib
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
import uuid

from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import Session, joinedload

from app.db.models.asset import (
    Asset,
    AssetRelationship,
    AssetTag,
    DiscoveryJob,
    AssetProvider,
    AssetStatus,
    AssetType,
    DiscoveryJobStatus,
    DiscoverySource,
    RelationshipType,
    RiskLevel,
)
from app.schemas.asset import (
    AssetCreate,
    AssetUpdate,
    AssetSearchRequest,
    AssetRelationshipCreate,
    AssetRelationshipUpdate,
    DiscoveryJobCreate,
    DiscoveryJobUpdate,
    AssetTagCreate,
    AssetTagUpdate,
)

logger = logging.getLogger(__name__)


class AssetNotFoundError(Exception):
    """Raised when an asset is not found."""
    pass


class AssetAlreadyExistsError(Exception):
    """Raised when trying to create an asset that already exists."""
    pass


class RelationshipNotFoundError(Exception):
    """Raised when a relationship is not found."""
    pass


class DiscoveryJobNotFoundError(Exception):
    """Raised when a discovery job is not found."""
    pass


class AssetService:
    """Service for managing assets and their relationships."""

    def __init__(self, db: Session):
        self.db = db

    # Asset CRUD operations
    def create_asset(self, asset_data: AssetCreate) -> Asset:
        """Create a new asset."""
        # Check if asset already exists with same provider and provider_id
        if asset_data.provider_id:
            existing = self.db.query(Asset).filter(
                and_(
                    Asset.provider == asset_data.provider,
                    Asset.provider_id == asset_data.provider_id
                )
            ).first()
            
            if existing:
                raise AssetAlreadyExistsError(
                    f"Asset with provider {asset_data.provider} and ID {asset_data.provider_id} already exists"
                )

        # Create configuration hash
        config_hash = None
        if asset_data.configuration:
            config_str = str(sorted(asset_data.configuration.items()))
            config_hash = hashlib.sha256(config_str.encode()).hexdigest()

        asset = Asset(
            **asset_data.model_dump(exclude_unset=True),
            configuration_hash=config_hash,
            discovered_at=datetime.utcnow(),
            last_seen=datetime.utcnow(),
        )

        self.db.add(asset)
        self.db.commit()
        self.db.refresh(asset)

        logger.info(f"Created asset: {asset.name} ({asset.id})")
        return asset

    def get_asset(self, asset_id: uuid.UUID) -> Asset:
        """Get an asset by ID."""
        asset = self.db.query(Asset).filter(Asset.id == asset_id).first()
        if not asset:
            raise AssetNotFoundError(f"Asset with ID {asset_id} not found")
        return asset

    def get_asset_by_provider_id(self, provider: AssetProvider, provider_id: str) -> Optional[Asset]:
        """Get an asset by provider and provider ID."""
        return self.db.query(Asset).filter(
            and_(
                Asset.provider == provider,
                Asset.provider_id == provider_id
            )
        ).first()

    def update_asset(self, asset_id: uuid.UUID, asset_data: AssetUpdate) -> Asset:
        """Update an asset."""
        asset = self.get_asset(asset_id)
        
        update_data = asset_data.model_dump(exclude_unset=True)
        
        # Update configuration hash if configuration changed
        if 'configuration' in update_data and update_data['configuration']:
            config_str = str(sorted(update_data['configuration'].items()))
            config_hash = hashlib.sha256(config_str.encode()).hexdigest()
            
            if config_hash != asset.configuration_hash:
                update_data['configuration_hash'] = config_hash
                update_data['last_configuration_change'] = datetime.utcnow()
                asset.change_count += 1

        # Update last_seen timestamp
        update_data['last_seen'] = datetime.utcnow()

        for field, value in update_data.items():
            setattr(asset, field, value)

        self.db.commit()
        self.db.refresh(asset)

        logger.info(f"Updated asset: {asset.name} ({asset.id})")
        return asset

    def delete_asset(self, asset_id: uuid.UUID) -> bool:
        """Delete an asset."""
        asset = self.get_asset(asset_id)
        
        # Delete related relationships
        self.db.query(AssetRelationship).filter(
            or_(
                AssetRelationship.source_asset_id == asset_id,
                AssetRelationship.target_asset_id == asset_id
            )
        ).delete()
        
        # Delete related tags
        self.db.query(AssetTag).filter(AssetTag.asset_id == asset_id).delete()
        
        self.db.delete(asset)
        self.db.commit()

        logger.info(f"Deleted asset: {asset.name} ({asset.id})")
        return True

    def search_assets(self, search_request: AssetSearchRequest) -> Tuple[List[Asset], int]:
        """Search assets with filters and pagination."""
        query = self.db.query(Asset)

        # Apply filters
        if search_request.query:
            search_term = f"%{search_request.query}%"
            query = query.filter(
                or_(
                    Asset.name.ilike(search_term),
                    Asset.description.ilike(search_term),
                    Asset.provider_id.ilike(search_term)
                )
            )

        if search_request.asset_types:
            query = query.filter(Asset.asset_type.in_(search_request.asset_types))

        if search_request.providers:
            query = query.filter(Asset.provider.in_(search_request.providers))

        if search_request.statuses:
            query = query.filter(Asset.status.in_(search_request.statuses))

        if search_request.environments:
            query = query.filter(Asset.environment.in_(search_request.environments))

        if search_request.owners:
            query = query.filter(Asset.owner.in_(search_request.owners))

        if search_request.teams:
            query = query.filter(Asset.team.in_(search_request.teams))

        if search_request.risk_levels:
            query = query.filter(Asset.risk_level.in_(search_request.risk_levels))

        if search_request.discovered_after:
            query = query.filter(Asset.discovered_at >= search_request.discovered_after)

        if search_request.discovered_before:
            query = query.filter(Asset.discovered_at <= search_request.discovered_before)

        # Tag filtering
        if search_request.tags:
            for key, value in search_request.tags.items():
                query = query.filter(Asset.tags[key].astext == value)

        # Get total count
        total = query.count()

        # Apply sorting
        sort_field = getattr(Asset, search_request.sort_by, Asset.name)
        if search_request.sort_order == "desc":
            query = query.order_by(desc(sort_field))
        else:
            query = query.order_by(sort_field)

        # Apply pagination
        offset = (search_request.page - 1) * search_request.size
        assets = query.offset(offset).limit(search_request.size).all()

        return assets, total

    def update_asset_last_seen(self, asset_id: uuid.UUID) -> Asset:
        """Update the last_seen timestamp for an asset."""
        asset = self.get_asset(asset_id)
        asset.last_seen = datetime.utcnow()
        self.db.commit()
        self.db.refresh(asset)
        return asset

    # Asset Relationship operations
    def create_relationship(self, relationship_data: AssetRelationshipCreate) -> AssetRelationship:
        """Create a new asset relationship."""
        # Verify source and target assets exist
        source_asset = self.get_asset(relationship_data.source_asset_id)
        target_asset = self.get_asset(relationship_data.target_asset_id)

        # Check if relationship already exists
        existing = self.db.query(AssetRelationship).filter(
            and_(
                AssetRelationship.source_asset_id == relationship_data.source_asset_id,
                AssetRelationship.target_asset_id == relationship_data.target_asset_id,
                AssetRelationship.relationship_type == relationship_data.relationship_type,
                AssetRelationship.protocol == relationship_data.protocol,
                AssetRelationship.port == relationship_data.port
            )
        ).first()

        if existing:
            # Update existing relationship
            existing.last_verified = datetime.utcnow()
            existing.is_active = True
            if relationship_data.confidence_score > existing.confidence_score:
                existing.confidence_score = relationship_data.confidence_score
            self.db.commit()
            self.db.refresh(existing)
            return existing

        relationship = AssetRelationship(
            **relationship_data.model_dump(exclude_unset=True),
            discovered_at=datetime.utcnow(),
            last_verified=datetime.utcnow(),
        )

        self.db.add(relationship)
        self.db.commit()
        self.db.refresh(relationship)

        logger.info(f"Created relationship: {source_asset.name} -> {target_asset.name} ({relationship.relationship_type})")
        return relationship

    def get_asset_relationships(self, asset_id: uuid.UUID, relationship_type: Optional[RelationshipType] = None) -> List[AssetRelationship]:
        """Get all relationships for an asset."""
        query = self.db.query(AssetRelationship).filter(
            or_(
                AssetRelationship.source_asset_id == asset_id,
                AssetRelationship.target_asset_id == asset_id
            )
        )

        if relationship_type:
            query = query.filter(AssetRelationship.relationship_type == relationship_type)

        return query.options(
            joinedload(AssetRelationship.source_asset),
            joinedload(AssetRelationship.target_asset)
        ).all()

    def update_relationship(self, relationship_id: uuid.UUID, relationship_data: AssetRelationshipUpdate) -> AssetRelationship:
        """Update an asset relationship."""
        relationship = self.db.query(AssetRelationship).filter(AssetRelationship.id == relationship_id).first()
        if not relationship:
            raise RelationshipNotFoundError(f"Relationship with ID {relationship_id} not found")

        update_data = relationship_data.model_dump(exclude_unset=True)
        update_data['last_verified'] = datetime.utcnow()

        for field, value in update_data.items():
            setattr(relationship, field, value)

        self.db.commit()
        self.db.refresh(relationship)

        logger.info(f"Updated relationship: {relationship.id}")
        return relationship

    def delete_relationship(self, relationship_id: uuid.UUID) -> bool:
        """Delete an asset relationship."""
        relationship = self.db.query(AssetRelationship).filter(AssetRelationship.id == relationship_id).first()
        if not relationship:
            raise RelationshipNotFoundError(f"Relationship with ID {relationship_id} not found")

        self.db.delete(relationship)
        self.db.commit()

        logger.info(f"Deleted relationship: {relationship.id}")
        return True

    # Asset Tag operations
    def add_asset_tag(self, tag_data: AssetTagCreate) -> AssetTag:
        """Add a tag to an asset."""
        # Verify asset exists
        asset = self.get_asset(tag_data.asset_id)

        # Check if tag already exists
        existing = self.db.query(AssetTag).filter(
            and_(
                AssetTag.asset_id == tag_data.asset_id,
                AssetTag.key == tag_data.key
            )
        ).first()

        if existing:
            # Update existing tag
            existing.value = tag_data.value
            existing.source = tag_data.source
            self.db.commit()
            self.db.refresh(existing)
            return existing

        tag = AssetTag(**tag_data.model_dump(exclude_unset=True))
        self.db.add(tag)
        self.db.commit()
        self.db.refresh(tag)

        logger.info(f"Added tag to asset {asset.name}: {tag.key}={tag.value}")
        return tag

    def get_asset_tags(self, asset_id: uuid.UUID) -> List[AssetTag]:
        """Get all tags for an asset."""
        return self.db.query(AssetTag).filter(AssetTag.asset_id == asset_id).all()

    def update_asset_tag(self, tag_id: uuid.UUID, tag_data: AssetTagUpdate) -> AssetTag:
        """Update an asset tag."""
        tag = self.db.query(AssetTag).filter(AssetTag.id == tag_id).first()
        if not tag:
            raise AssetNotFoundError(f"Tag with ID {tag_id} not found")

        update_data = tag_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tag, field, value)

        self.db.commit()
        self.db.refresh(tag)

        logger.info(f"Updated tag: {tag.key}={tag.value}")
        return tag

    def delete_asset_tag(self, tag_id: uuid.UUID) -> bool:
        """Delete an asset tag."""
        tag = self.db.query(AssetTag).filter(AssetTag.id == tag_id).first()
        if not tag:
            raise AssetNotFoundError(f"Tag with ID {tag_id} not found")

        self.db.delete(tag)
        self.db.commit()

        logger.info(f"Deleted tag: {tag.key}={tag.value}")
        return True

    # Statistics and analytics
    def get_asset_statistics(self) -> Dict[str, Any]:
        """Get asset statistics and analytics."""
        stats = {}

        # Total assets
        stats['total_assets'] = self.db.query(Asset).count()

        # Assets by type
        type_counts = self.db.query(Asset.asset_type, func.count(Asset.id)).group_by(Asset.asset_type).all()
        stats['assets_by_type'] = {str(asset_type): count for asset_type, count in type_counts}

        # Assets by provider
        provider_counts = self.db.query(Asset.provider, func.count(Asset.id)).group_by(Asset.provider).all()
        stats['assets_by_provider'] = {str(provider): count for provider, count in provider_counts}

        # Assets by status
        status_counts = self.db.query(Asset.status, func.count(Asset.id)).group_by(Asset.status).all()
        stats['assets_by_status'] = {str(status): count for status, count in status_counts}

        # Assets by environment
        env_counts = self.db.query(Asset.environment, func.count(Asset.id)).group_by(Asset.environment).all()
        stats['assets_by_environment'] = {env or 'unknown': count for env, count in env_counts}

        # Assets by risk level
        risk_counts = self.db.query(Asset.risk_level, func.count(Asset.id)).group_by(Asset.risk_level).all()
        stats['assets_by_risk_level'] = {str(risk_level): count for risk_level, count in risk_counts}

        # Discovery sources
        discovery_counts = self.db.query(Asset.discovery_source, func.count(Asset.id)).group_by(Asset.discovery_source).all()
        stats['discovery_sources'] = {str(source): count for source, count in discovery_counts}

        # Recent discoveries (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(days=1)
        stats['recent_discoveries'] = self.db.query(Asset).filter(Asset.discovered_at >= yesterday).count()

        # Relationships
        stats['total_relationships'] = self.db.query(AssetRelationship).count()
        rel_type_counts = self.db.query(AssetRelationship.relationship_type, func.count(AssetRelationship.id)).group_by(AssetRelationship.relationship_type).all()
        stats['relationships_by_type'] = {str(rel_type): count for rel_type, count in rel_type_counts}

        # Compliance summary
        compliance_counts = self.db.query(Asset.compliance_status, func.count(Asset.id)).group_by(Asset.compliance_status).all()
        stats['compliance_summary'] = {status: count for status, count in compliance_counts}

        # Health summary
        health_counts = self.db.query(Asset.health_status, func.count(Asset.id)).group_by(Asset.health_status).all()
        stats['health_summary'] = {status or 'unknown': count for status, count in health_counts}

        return stats

    # Discovery Job operations
    def create_discovery_job(self, job_data: DiscoveryJobCreate) -> DiscoveryJob:
        """Create a new discovery job."""
        job = DiscoveryJob(
            **job_data.model_dump(exclude_unset=True),
            status=DiscoveryJobStatus.PENDING,
        )

        self.db.add(job)
        self.db.commit()
        self.db.refresh(job)

        logger.info(f"Created discovery job: {job.name} ({job.id})")
        return job

    def get_discovery_job(self, job_id: uuid.UUID) -> DiscoveryJob:
        """Get a discovery job by ID."""
        job = self.db.query(DiscoveryJob).filter(DiscoveryJob.id == job_id).first()
        if not job:
            raise DiscoveryJobNotFoundError(f"Discovery job with ID {job_id} not found")
        return job

    def update_discovery_job(self, job_id: uuid.UUID, job_data: DiscoveryJobUpdate) -> DiscoveryJob:
        """Update a discovery job."""
        job = self.get_discovery_job(job_id)

        update_data = job_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(job, field, value)

        self.db.commit()
        self.db.refresh(job)

        logger.info(f"Updated discovery job: {job.name} ({job.id})")
        return job

    def start_discovery_job(self, job_id: uuid.UUID, executor: Optional[str] = None) -> DiscoveryJob:
        """Start a discovery job."""
        job = self.get_discovery_job(job_id)

        if job.status != DiscoveryJobStatus.PENDING:
            raise ValueError(f"Cannot start job in status: {job.status}")

        job.status = DiscoveryJobStatus.RUNNING
        job.started_at = datetime.utcnow()
        job.executor = executor

        self.db.commit()
        self.db.refresh(job)

        logger.info(f"Started discovery job: {job.name} ({job.id})")
        return job

    def complete_discovery_job(self, job_id: uuid.UUID,
                             assets_discovered: int = 0,
                             assets_updated: int = 0,
                             relationships_discovered: int = 0,
                             errors_count: int = 0,
                             execution_log: Optional[List[Dict[str, Any]]] = None,
                             error_details: Optional[List[Dict[str, Any]]] = None) -> DiscoveryJob:
        """Complete a discovery job."""
        job = self.get_discovery_job(job_id)

        if job.status != DiscoveryJobStatus.RUNNING:
            raise ValueError(f"Cannot complete job in status: {job.status}")

        job.status = DiscoveryJobStatus.COMPLETED if errors_count == 0 else DiscoveryJobStatus.PARTIAL
        job.completed_at = datetime.utcnow()

        if job.started_at:
            job.duration_seconds = int((job.completed_at - job.started_at).total_seconds())

        job.assets_discovered = assets_discovered
        job.assets_updated = assets_updated
        job.relationships_discovered = relationships_discovered
        job.errors_count = errors_count
        job.execution_log = execution_log
        job.error_details = error_details

        self.db.commit()
        self.db.refresh(job)

        logger.info(f"Completed discovery job: {job.name} ({job.id}) - Assets: {assets_discovered}, Errors: {errors_count}")
        return job

    def fail_discovery_job(self, job_id: uuid.UUID,
                          error_details: Optional[List[Dict[str, Any]]] = None) -> DiscoveryJob:
        """Mark a discovery job as failed."""
        job = self.get_discovery_job(job_id)

        job.status = DiscoveryJobStatus.FAILED
        job.completed_at = datetime.utcnow()

        if job.started_at:
            job.duration_seconds = int((job.completed_at - job.started_at).total_seconds())

        job.error_details = error_details

        self.db.commit()
        self.db.refresh(job)

        logger.error(f"Failed discovery job: {job.name} ({job.id})")
        return job

    def get_discovery_jobs(self,
                          job_type: Optional[str] = None,
                          status: Optional[DiscoveryJobStatus] = None,
                          page: int = 1,
                          size: int = 20) -> Tuple[List[DiscoveryJob], int]:
        """Get discovery jobs with filtering and pagination."""
        query = self.db.query(DiscoveryJob)

        if job_type:
            query = query.filter(DiscoveryJob.job_type == job_type)

        if status:
            query = query.filter(DiscoveryJob.status == status)

        total = query.count()

        # Apply pagination
        offset = (page - 1) * size
        jobs = query.order_by(desc(DiscoveryJob.created_at)).offset(offset).limit(size).all()

        return jobs, total

    def delete_discovery_job(self, job_id: uuid.UUID) -> bool:
        """Delete a discovery job."""
        job = self.get_discovery_job(job_id)

        # Update assets to remove job reference
        self.db.query(Asset).filter(Asset.discovery_job_id == job_id).update({"discovery_job_id": None})

        self.db.delete(job)
        self.db.commit()

        logger.info(f"Deleted discovery job: {job.name} ({job.id})")
        return True
