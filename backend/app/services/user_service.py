"""User Management Service for CRUD Operations and Administration.

This module provides comprehensive user management services for the Blast-Radius
Security Tool, including user lifecycle management, role assignment, session
management, and API key operations with full audit trail support.

The service supports:
- Complete user lifecycle management (create, read, update, delete)
- Advanced user search with filtering and pagination capabilities
- Role assignment and management with expiration support
- Session tracking and management for security monitoring
- API key creation, management, and revocation
- User statistics and analytics for administrative reporting
- Comprehensive audit logging for all user operations

Example:
    Basic user management:
        >>> user_service = UserService(db)
        >>> user = user_service.create_user(
        ...     UserCreate(
        ...         username="john.doe",
        ...         email="<EMAIL>",
        ...         full_name="<PERSON>",
        ...         password="secure_password"
        ...     )
        ... )

    Role management:
        >>> success = user_service.assign_roles(
        ...     user.id,
        ...     ["security_analyst", "incident_responder"],
        ...     assigned_by_user_id=admin_user.id
        ... )

Security Features:
- Secure password hashing using bcrypt
- Soft delete functionality to maintain audit trails
- Session management with automatic cleanup
- API key management with IP restrictions and rate limiting
- Role-based access control with granular permissions
- Comprehensive audit logging for compliance

Attributes:
    UserNotFoundError: Exception for missing users.
    UserAlreadyExistsError: Exception for duplicate users.
    InsufficientPermissionsError: Exception for permission violations.
    UserService: Main user management service class.
"""

from datetime import datetime, timedelta
import logging
from typing import Any
import uuid

from sqlalchemy import and_, func, or_
from sqlalchemy.orm import Session

from app.core.security import generate_api_key, get_password_hash
from app.db.models.user import User, UserAPIKey, UserRole, UserSession
from app.schemas.user import UserCreate, UserUpdate

logger = logging.getLogger(__name__)


class UserNotFoundError(Exception):
    """User not found error."""


class UserAlreadyExistsError(Exception):
    """User already exists error."""


class InsufficientPermissionsError(Exception):
    """Insufficient permissions error."""


class UserService:
    """Service for user management operations."""

    def __init__(self, db: Session):
        """
        Initialize user service.

        Args:
            db: Database session
        """
        self.db = db

    def create_user(
        self, user_data: UserCreate, created_by_user_id: uuid.UUID | None = None
    ) -> User:
        """
        Create a new user.

        Args:
            user_data: User creation data
            created_by_user_id: ID of user creating this user

        Returns:
            Created User object

        Raises:
            UserAlreadyExistsError: If username or email already exists
        """
        # Check if username already exists
        existing_user = (
            self.db.query(User)
            .filter(
                or_(
                    User.username == user_data.username.lower(),
                    User.email == user_data.email.lower(),
                )
            )
            .filter(User.is_deleted == False)
            .first()
        )

        if existing_user:
            if existing_user.username == user_data.username.lower():
                raise UserAlreadyExistsError("Username already exists")
            raise UserAlreadyExistsError("Email already exists")

        # Create user
        user = User(
            username=user_data.username.lower(),
            email=user_data.email.lower(),
            full_name=user_data.full_name,
            hashed_password=get_password_hash(user_data.password),
            phone_number=user_data.phone_number,
            department=user_data.department,
            job_title=user_data.job_title,
            timezone=user_data.timezone,
            language=user_data.language,
            theme=user_data.theme,
            is_active=user_data.is_active,
            require_password_change=user_data.require_password_change,
            manager_id=uuid.UUID(user_data.manager_id)
            if user_data.manager_id
            else None,
            created_by=created_by_user_id,
        )

        self.db.add(user)
        self.db.flush()  # Get user ID

        # Assign roles
        if user_data.roles:
            self._assign_roles_to_user(user.id, user_data.roles, created_by_user_id)

        self.db.commit()

        logger.info(f"User created: {user.username} (ID: {user.id})")
        return user

    def get_user_by_id(self, user_id: uuid.UUID) -> User | None:
        """
        Get user by ID.

        Args:
            user_id: User ID

        Returns:
            User object if found, None otherwise
        """
        return (
            self.db.query(User)
            .filter(and_(User.id == user_id, User.is_deleted == False))
            .first()
        )

    def get_user_by_username(self, username: str) -> User | None:
        """
        Get user by username.

        Args:
            username: Username

        Returns:
            User object if found, None otherwise
        """
        return (
            self.db.query(User)
            .filter(and_(User.username == username.lower(), User.is_deleted == False))
            .first()
        )

    def get_user_by_email(self, email: str) -> User | None:
        """
        Get user by email.

        Args:
            email: Email address

        Returns:
            User object if found, None otherwise
        """
        return (
            self.db.query(User)
            .filter(and_(User.email == email.lower(), User.is_deleted == False))
            .first()
        )

    def update_user(
        self,
        user_id: uuid.UUID,
        user_data: UserUpdate,
        updated_by_user_id: uuid.UUID | None = None,
    ) -> User:
        """
        Update user information.

        Args:
            user_id: User ID to update
            user_data: Update data
            updated_by_user_id: ID of user performing update

        Returns:
            Updated User object

        Raises:
            UserNotFoundError: If user not found
            UserAlreadyExistsError: If email already exists
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError("User not found")

        # Check email uniqueness if being updated
        if user_data.email and user_data.email.lower() != user.email:
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user and existing_user.id != user_id:
                raise UserAlreadyExistsError("Email already exists")

        # Update fields
        update_data = user_data.dict(exclude_unset=True)
        if "email" in update_data:
            update_data["email"] = update_data["email"].lower()

        user.update_from_dict(update_data, updated_by_user_id)

        self.db.commit()

        logger.info(f"User updated: {user.username} (ID: {user.id})")
        return user

    def delete_user(
        self, user_id: uuid.UUID, deleted_by_user_id: uuid.UUID | None = None
    ) -> bool:
        """
        Soft delete user.

        Args:
            user_id: User ID to delete
            deleted_by_user_id: ID of user performing deletion

        Returns:
            True if deleted successfully, False if user not found
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return False

        # Soft delete user
        user.soft_delete(deleted_by_user_id)

        # Terminate all active sessions
        active_sessions = (
            self.db.query(UserSession)
            .filter(and_(UserSession.user_id == user_id, UserSession.is_active == True))
            .all()
        )

        for session in active_sessions:
            session.terminate_session("user_deleted")

        # Deactivate all API keys
        api_keys = (
            self.db.query(UserAPIKey)
            .filter(and_(UserAPIKey.user_id == user_id, UserAPIKey.is_active == True))
            .all()
        )

        for api_key in api_keys:
            api_key.revoke()

        self.db.commit()

        logger.info(f"User deleted: {user.username} (ID: {user.id})")
        return True

    def list_users(
        self,
        page: int = 1,
        size: int = 20,
        search: str | None = None,
        role_filter: str | None = None,
        active_only: bool = True,
        department_filter: str | None = None,
    ) -> tuple[list[User], int]:
        """
        List users with pagination and filtering.

        Args:
            page: Page number (1-based)
            size: Page size
            search: Search term for username, email, or full name
            role_filter: Filter by role name
            active_only: Only return active users
            department_filter: Filter by department

        Returns:
            Tuple of (users list, total count)
        """
        query = self.db.query(User).filter(User.is_deleted == False)

        # Apply filters
        if active_only:
            query = query.filter(User.is_active == True)

        if search:
            search_term = f"%{search.lower()}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_term),
                    User.email.ilike(search_term),
                    User.full_name.ilike(search_term),
                )
            )

        if department_filter:
            query = query.filter(User.department.ilike(f"%{department_filter}%"))

        if role_filter:
            # Join with roles to filter by role name
            query = query.join(User.roles).filter(UserRole.name == role_filter)

        # Get total count
        total = query.count()

        # Apply pagination
        offset = (page - 1) * size
        users = query.offset(offset).limit(size).all()

        return users, total

    def assign_roles(
        self,
        user_id: uuid.UUID,
        role_names: list[str],
        assigned_by_user_id: uuid.UUID | None = None,
        expires_at: datetime | None = None,
    ) -> bool:
        """
        Assign roles to user.

        Args:
            user_id: User ID
            role_names: List of role names to assign
            assigned_by_user_id: ID of user assigning roles
            expires_at: Optional expiration time for roles

        Returns:
            True if successful, False if user not found
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return False

        self._assign_roles_to_user(user_id, role_names, assigned_by_user_id, expires_at)
        self.db.commit()

        logger.info(f"Roles assigned to user {user.username}: {role_names}")
        return True

    def _assign_roles_to_user(
        self,
        user_id: uuid.UUID,
        role_names: list[str],
        assigned_by_user_id: uuid.UUID | None = None,
        expires_at: datetime | None = None,
    ) -> None:
        """
        Internal method to assign roles to user.

        Args:
            user_id: User ID
            role_names: List of role names to assign
            assigned_by_user_id: ID of user assigning roles
            expires_at: Optional expiration time for roles
        """
        # Get role objects
        roles = (
            self.db.query(UserRole)
            .filter(and_(UserRole.name.in_(role_names), UserRole.is_active == True))
            .all()
        )

        # Clear existing roles
        user = self.get_user_by_id(user_id)
        user.roles.clear()

        # Assign new roles
        for role in roles:
            user.roles.append(role)

    def remove_roles(
        self,
        user_id: uuid.UUID,
        role_names: list[str],
        removed_by_user_id: uuid.UUID | None = None,
    ) -> bool:
        """
        Remove roles from user.

        Args:
            user_id: User ID
            role_names: List of role names to remove
            removed_by_user_id: ID of user removing roles

        Returns:
            True if successful, False if user not found
        """
        user = self.get_user_by_id(user_id)
        if not user:
            return False

        # Get roles to remove
        roles_to_remove = (
            self.db.query(UserRole).filter(UserRole.name.in_(role_names)).all()
        )

        # Remove roles
        for role in roles_to_remove:
            if role in user.roles:
                user.roles.remove(role)

        self.db.commit()

        logger.info(f"Roles removed from user {user.username}: {role_names}")
        return True

    def create_api_key(
        self,
        user_id: uuid.UUID,
        name: str,
        description: str | None = None,
        scopes: list[str] | None = None,
        allowed_ips: list[str] | None = None,
        rate_limit: int | None = None,
        expires_at: datetime | None = None,
        created_by_user_id: uuid.UUID | None = None,
    ) -> tuple[UserAPIKey, str]:
        """
        Create API key for user.

        Args:
            user_id: User ID
            name: API key name
            description: API key description
            scopes: List of allowed scopes/permissions
            allowed_ips: List of allowed IP addresses
            rate_limit: Rate limit in requests per minute
            expires_at: Expiration time
            created_by_user_id: ID of user creating the key

        Returns:
            Tuple of (UserAPIKey object, secret key)

        Raises:
            UserNotFoundError: If user not found
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError("User not found")

        # Generate API key
        secret_key = generate_api_key()
        key_prefix = secret_key[:8]  # First 8 characters for identification

        # Hash the key for storage
        from app.core.security import get_password_hash

        key_hash = get_password_hash(secret_key)

        # Create API key record
        api_key = UserAPIKey(
            user_id=user_id,
            name=name,
            description=description,
            key_hash=key_hash,
            key_prefix=key_prefix,
            scopes=scopes,
            allowed_ips=allowed_ips,
            rate_limit=rate_limit,
            expires_at=expires_at,
            created_by=created_by_user_id,
        )

        self.db.add(api_key)
        self.db.commit()

        logger.info(f"API key created for user {user.username}: {name}")
        return api_key, secret_key

    def revoke_api_key(
        self, api_key_id: uuid.UUID, revoked_by_user_id: uuid.UUID | None = None
    ) -> bool:
        """
        Revoke API key.

        Args:
            api_key_id: API key ID
            revoked_by_user_id: ID of user revoking the key

        Returns:
            True if successful, False if key not found
        """
        api_key = self.db.query(UserAPIKey).filter(UserAPIKey.id == api_key_id).first()

        if not api_key:
            return False

        api_key.revoke()
        self.db.commit()

        logger.info(f"API key revoked: {api_key.name} (ID: {api_key.id})")
        return True

    def get_user_sessions(
        self, user_id: uuid.UUID, active_only: bool = True
    ) -> list[UserSession]:
        """
        Get user sessions.

        Args:
            user_id: User ID
            active_only: Only return active sessions

        Returns:
            List of UserSession objects
        """
        query = self.db.query(UserSession).filter(UserSession.user_id == user_id)

        if active_only:
            query = query.filter(
                and_(
                    UserSession.is_active == True,
                    UserSession.expires_at > datetime.utcnow(),
                )
            )

        return query.order_by(UserSession.last_activity_at.desc()).all()

    def terminate_user_session(
        self, session_id: uuid.UUID, reason: str = "admin_terminated"
    ) -> bool:
        """
        Terminate user session.

        Args:
            session_id: Session ID
            reason: Termination reason

        Returns:
            True if successful, False if session not found
        """
        session = (
            self.db.query(UserSession).filter(UserSession.id == session_id).first()
        )

        if not session:
            return False

        session.terminate_session(reason)
        self.db.commit()

        logger.info(f"Session terminated: {session_id} (reason: {reason})")
        return True

    def get_user_statistics(self) -> dict[str, Any]:
        """
        Get user statistics.

        Returns:
            Dictionary with user statistics
        """
        total_users = self.db.query(User).filter(User.is_deleted == False).count()
        active_users = (
            self.db.query(User)
            .filter(and_(User.is_deleted == False, User.is_active == True))
            .count()
        )
        locked_users = (
            self.db.query(User)
            .filter(and_(User.is_deleted == False, User.is_locked == True))
            .count()
        )

        # Users by role
        role_stats = (
            self.db.query(UserRole.name, func.count(User.id).label("count"))
            .join(User.roles)
            .filter(User.is_deleted == False)
            .group_by(UserRole.name)
            .all()
        )

        # Recent logins (last 24 hours)
        recent_logins = (
            self.db.query(User)
            .filter(
                and_(
                    User.is_deleted == False,
                    User.last_login_at > datetime.utcnow() - timedelta(hours=24),
                )
            )
            .count()
        )

        return {
            "total_users": total_users,
            "active_users": active_users,
            "locked_users": locked_users,
            "inactive_users": total_users - active_users,
            "recent_logins_24h": recent_logins,
            "users_by_role": {role: count for role, count in role_stats},
        }
