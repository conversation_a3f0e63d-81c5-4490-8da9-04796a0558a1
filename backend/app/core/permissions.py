"""Role-Based Access Control and Permissions System.

This module provides a comprehensive role-based access control (RBAC) system
for the Blast-Radius Security Tool, defining user roles, permissions, and
access control mechanisms for secure resource management.

The module supports:
- Hierarchical user role definitions with specific security responsibilities
- Granular permission system covering all system operations
- Role-to-permission mapping with inheritance support
- Permission checking utilities for access control
- Resource-based access control with action-specific permissions
- Administrative privilege management and validation

Example:
    Basic permission checking:
        >>> user_roles = [UserRole.SOC_OPERATOR]
        >>> can_create = has_permission(user_roles, Permission.CREATE_INCIDENT)
        >>> user_perms = get_user_permissions(user_roles)

    Resource access validation:
        >>> can_access = can_access_resource(
        ...     user_roles, "asset", "read"
        ... )

Security Roles:
- ADMIN: Full system administrator with all permissions
- SOC_OPERATOR: Security operations center operator for monitoring
- SECURITY_ARCHITECT: Security design and risk assessment specialist
- RED_TEAM_MEMBER: Offensive security testing and attack simulation
- PURPLE_TEAM_MEMBER: Collaborative security testing and validation
- ANALYST: Security analysis with read and reporting capabilities
- VIEWER: Read-only access to dashboards and reports

Permission Categories:
- User Management: User lifecycle and role management
- Asset Management: Asset discovery, tracking, and maintenance
- Security Events: Incident response and event management
- Attack Path Analysis: Attack simulation and path discovery
- Threat Intelligence: IOC management and threat data
- Monitoring & Dashboards: Real-time monitoring and visualization
- Reports & Analytics: Security reporting and data analysis
- System Configuration: System settings and integration management

Attributes:
    UserRole: Enumeration of available user roles.
    Permission: Enumeration of system permissions.
    RolePermissions: Dataclass for role-permission mapping.
    ROLE_PERMISSIONS: Complete role-to-permission mapping dictionary.
"""

from dataclasses import dataclass
from enum import Enum


class UserRole(str, Enum):
    """User roles in the system."""

    ADMIN = "admin"
    SOC_OPERATOR = "soc_operator"
    SECURITY_ARCHITECT = "security_architect"
    RED_TEAM_MEMBER = "red_team_member"
    PURPLE_TEAM_MEMBER = "purple_team_member"
    ANALYST = "analyst"
    VIEWER = "viewer"


class Permission(str, Enum):
    """System permissions."""

    # User Management
    CREATE_USER = "create_user"
    READ_USER = "read_user"
    UPDATE_USER = "update_user"
    DELETE_USER = "delete_user"
    MANAGE_USER_ROLES = "manage_user_roles"

    # Authentication & Sessions
    MANAGE_SESSIONS = "manage_sessions"
    VIEW_AUDIT_LOGS = "view_audit_logs"
    MANAGE_API_KEYS = "manage_api_keys"

    # Asset Management
    CREATE_ASSET = "create_asset"
    READ_ASSET = "read_asset"
    UPDATE_ASSET = "update_asset"
    DELETE_ASSET = "delete_asset"
    IMPORT_ASSETS = "import_assets"
    EXPORT_ASSETS = "export_assets"

    # Security Events & Incidents
    VIEW_SECURITY_EVENTS = "view_security_events"
    CREATE_INCIDENT = "create_incident"
    UPDATE_INCIDENT = "update_incident"
    ASSIGN_INCIDENT = "assign_incident"
    CLOSE_INCIDENT = "close_incident"
    DELETE_INCIDENT = "delete_incident"

    # Attack Path Analysis
    VIEW_ATTACK_PATHS = "view_attack_paths"
    CREATE_ATTACK_SCENARIO = "create_attack_scenario"
    RUN_ATTACK_SIMULATION = "run_attack_simulation"
    EXPORT_ATTACK_PATHS = "export_attack_paths"

    # Threat Intelligence
    VIEW_THREAT_INTEL = "view_threat_intel"
    MANAGE_THREAT_INTEL = "manage_threat_intel"
    CREATE_IOC = "create_ioc"
    UPDATE_IOC = "update_ioc"
    DELETE_IOC = "delete_ioc"

    # Monitoring & Dashboards
    VIEW_DASHBOARD = "view_dashboard"
    CREATE_DASHBOARD = "create_dashboard"
    UPDATE_DASHBOARD = "update_dashboard"
    DELETE_DASHBOARD = "delete_dashboard"
    CONFIGURE_ALERTS = "configure_alerts"

    # Reports & Analytics
    VIEW_REPORTS = "view_reports"
    CREATE_REPORTS = "create_reports"
    EXPORT_REPORTS = "export_reports"
    SCHEDULE_REPORTS = "schedule_reports"

    # System Configuration
    MANAGE_SYSTEM_CONFIG = "manage_system_config"
    MANAGE_INTEGRATIONS = "manage_integrations"
    VIEW_SYSTEM_HEALTH = "view_system_health"
    MANAGE_BACKUPS = "manage_backups"

    # Remediation & Response
    EXECUTE_REMEDIATION = "execute_remediation"
    APPROVE_REMEDIATION = "approve_remediation"
    CREATE_PLAYBOOK = "create_playbook"
    EXECUTE_PLAYBOOK = "execute_playbook"

    # Cloud Integration
    MANAGE_CLOUD_CONNECTIONS = "manage_cloud_connections"
    VIEW_CLOUD_ASSETS = "view_cloud_assets"
    SYNC_CLOUD_DATA = "sync_cloud_data"

    # ServiceNow Integration
    MANAGE_SERVICENOW_CONFIG = "manage_servicenow_config"
    SYNC_SERVICENOW_DATA = "sync_servicenow_data"
    CREATE_SERVICENOW_INCIDENT = "create_servicenow_incident"


@dataclass
class RolePermissions:
    """Role permissions mapping."""

    role: UserRole
    permissions: set[Permission]
    description: str
    inherits_from: UserRole | None = None


# Define role hierarchy and permissions
ROLE_PERMISSIONS: dict[UserRole, RolePermissions] = {
    UserRole.ADMIN: RolePermissions(
        role=UserRole.ADMIN,
        description="Full system administrator with all permissions",
        permissions={
            # All permissions for admin
            Permission.CREATE_USER,
            Permission.READ_USER,
            Permission.UPDATE_USER,
            Permission.DELETE_USER,
            Permission.MANAGE_USER_ROLES,
            Permission.MANAGE_SESSIONS,
            Permission.VIEW_AUDIT_LOGS,
            Permission.MANAGE_API_KEYS,
            Permission.CREATE_ASSET,
            Permission.READ_ASSET,
            Permission.UPDATE_ASSET,
            Permission.DELETE_ASSET,
            Permission.IMPORT_ASSETS,
            Permission.EXPORT_ASSETS,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.CREATE_INCIDENT,
            Permission.UPDATE_INCIDENT,
            Permission.ASSIGN_INCIDENT,
            Permission.CLOSE_INCIDENT,
            Permission.DELETE_INCIDENT,
            Permission.VIEW_ATTACK_PATHS,
            Permission.CREATE_ATTACK_SCENARIO,
            Permission.RUN_ATTACK_SIMULATION,
            Permission.EXPORT_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.MANAGE_THREAT_INTEL,
            Permission.CREATE_IOC,
            Permission.UPDATE_IOC,
            Permission.DELETE_IOC,
            Permission.VIEW_DASHBOARD,
            Permission.CREATE_DASHBOARD,
            Permission.UPDATE_DASHBOARD,
            Permission.DELETE_DASHBOARD,
            Permission.CONFIGURE_ALERTS,
            Permission.VIEW_REPORTS,
            Permission.CREATE_REPORTS,
            Permission.EXPORT_REPORTS,
            Permission.SCHEDULE_REPORTS,
            Permission.MANAGE_SYSTEM_CONFIG,
            Permission.MANAGE_INTEGRATIONS,
            Permission.VIEW_SYSTEM_HEALTH,
            Permission.MANAGE_BACKUPS,
            Permission.EXECUTE_REMEDIATION,
            Permission.APPROVE_REMEDIATION,
            Permission.CREATE_PLAYBOOK,
            Permission.EXECUTE_PLAYBOOK,
            Permission.MANAGE_CLOUD_CONNECTIONS,
            Permission.VIEW_CLOUD_ASSETS,
            Permission.SYNC_CLOUD_DATA,
            Permission.MANAGE_SERVICENOW_CONFIG,
            Permission.SYNC_SERVICENOW_DATA,
            Permission.CREATE_SERVICENOW_INCIDENT,
        },
    ),
    UserRole.SOC_OPERATOR: RolePermissions(
        role=UserRole.SOC_OPERATOR,
        description="SOC operator for monitoring and incident response",
        permissions={
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.CREATE_INCIDENT,
            Permission.UPDATE_INCIDENT,
            Permission.ASSIGN_INCIDENT,
            Permission.CLOSE_INCIDENT,
            Permission.VIEW_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.VIEW_DASHBOARD,
            Permission.UPDATE_DASHBOARD,
            Permission.CONFIGURE_ALERTS,
            Permission.VIEW_REPORTS,
            Permission.CREATE_REPORTS,
            Permission.EXPORT_REPORTS,
            Permission.VIEW_SYSTEM_HEALTH,
            Permission.EXECUTE_REMEDIATION,
            Permission.EXECUTE_PLAYBOOK,
            Permission.VIEW_CLOUD_ASSETS,
            Permission.CREATE_SERVICENOW_INCIDENT,
        },
    ),
    UserRole.SECURITY_ARCHITECT: RolePermissions(
        role=UserRole.SECURITY_ARCHITECT,
        description="Security architect for design and risk assessment",
        permissions={
            Permission.READ_USER,
            Permission.CREATE_ASSET,
            Permission.READ_ASSET,
            Permission.UPDATE_ASSET,
            Permission.IMPORT_ASSETS,
            Permission.EXPORT_ASSETS,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.CREATE_INCIDENT,
            Permission.UPDATE_INCIDENT,
            Permission.VIEW_ATTACK_PATHS,
            Permission.CREATE_ATTACK_SCENARIO,
            Permission.EXPORT_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.MANAGE_THREAT_INTEL,
            Permission.CREATE_IOC,
            Permission.UPDATE_IOC,
            Permission.VIEW_DASHBOARD,
            Permission.CREATE_DASHBOARD,
            Permission.UPDATE_DASHBOARD,
            Permission.CONFIGURE_ALERTS,
            Permission.VIEW_REPORTS,
            Permission.CREATE_REPORTS,
            Permission.EXPORT_REPORTS,
            Permission.SCHEDULE_REPORTS,
            Permission.MANAGE_INTEGRATIONS,
            Permission.VIEW_SYSTEM_HEALTH,
            Permission.APPROVE_REMEDIATION,
            Permission.CREATE_PLAYBOOK,
            Permission.MANAGE_CLOUD_CONNECTIONS,
            Permission.VIEW_CLOUD_ASSETS,
            Permission.SYNC_CLOUD_DATA,
            Permission.MANAGE_SERVICENOW_CONFIG,
        },
    ),
    UserRole.RED_TEAM_MEMBER: RolePermissions(
        role=UserRole.RED_TEAM_MEMBER,
        description="Red team member for attack simulation and testing",
        permissions={
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.VIEW_ATTACK_PATHS,
            Permission.CREATE_ATTACK_SCENARIO,
            Permission.RUN_ATTACK_SIMULATION,
            Permission.EXPORT_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.VIEW_DASHBOARD,
            Permission.CREATE_DASHBOARD,
            Permission.UPDATE_DASHBOARD,
            Permission.VIEW_REPORTS,
            Permission.CREATE_REPORTS,
            Permission.EXPORT_REPORTS,
            Permission.VIEW_CLOUD_ASSETS,
        },
    ),
    UserRole.PURPLE_TEAM_MEMBER: RolePermissions(
        role=UserRole.PURPLE_TEAM_MEMBER,
        description="Purple team member for collaborative security testing",
        permissions={
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.CREATE_INCIDENT,
            Permission.UPDATE_INCIDENT,
            Permission.VIEW_ATTACK_PATHS,
            Permission.CREATE_ATTACK_SCENARIO,
            Permission.RUN_ATTACK_SIMULATION,
            Permission.EXPORT_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.CREATE_IOC,
            Permission.UPDATE_IOC,
            Permission.VIEW_DASHBOARD,
            Permission.CREATE_DASHBOARD,
            Permission.UPDATE_DASHBOARD,
            Permission.VIEW_REPORTS,
            Permission.CREATE_REPORTS,
            Permission.EXPORT_REPORTS,
            Permission.EXECUTE_REMEDIATION,
            Permission.CREATE_PLAYBOOK,
            Permission.EXECUTE_PLAYBOOK,
            Permission.VIEW_CLOUD_ASSETS,
        },
    ),
    UserRole.ANALYST: RolePermissions(
        role=UserRole.ANALYST,
        description="Security analyst with read and analysis permissions",
        permissions={
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.VIEW_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.VIEW_DASHBOARD,
            Permission.VIEW_REPORTS,
            Permission.CREATE_REPORTS,
            Permission.EXPORT_REPORTS,
            Permission.VIEW_CLOUD_ASSETS,
        },
    ),
    UserRole.VIEWER: RolePermissions(
        role=UserRole.VIEWER,
        description="Read-only access to dashboards and reports",
        permissions={
            Permission.READ_USER,
            Permission.READ_ASSET,
            Permission.VIEW_SECURITY_EVENTS,
            Permission.VIEW_ATTACK_PATHS,
            Permission.VIEW_THREAT_INTEL,
            Permission.VIEW_DASHBOARD,
            Permission.VIEW_REPORTS,
            Permission.VIEW_CLOUD_ASSETS,
        },
    ),
}


def get_role_permissions(role: UserRole) -> set[Permission]:
    """
    Get permissions for a specific role.

    Args:
        role: User role

    Returns:
        Set of permissions for the role
    """
    role_config = ROLE_PERMISSIONS.get(role)
    if not role_config:
        return set()

    permissions = role_config.permissions.copy()

    # Handle inheritance if needed
    if role_config.inherits_from:
        parent_permissions = get_role_permissions(role_config.inherits_from)
        permissions.update(parent_permissions)

    return permissions


def has_permission(user_roles: list[UserRole], required_permission: Permission) -> bool:
    """
    Check if user has required permission based on their roles.

    Args:
        user_roles: List of user roles
        required_permission: Required permission to check

    Returns:
        True if user has permission, False otherwise
    """
    for role in user_roles:
        role_permissions = get_role_permissions(role)
        if required_permission in role_permissions:
            return True
    return False


def get_user_permissions(user_roles: list[UserRole]) -> set[Permission]:
    """
    Get all permissions for a user based on their roles.

    Args:
        user_roles: List of user roles

    Returns:
        Set of all permissions for the user
    """
    all_permissions = set()
    for role in user_roles:
        role_permissions = get_role_permissions(role)
        all_permissions.update(role_permissions)
    return all_permissions


def is_admin(user_roles: list[UserRole]) -> bool:
    """
    Check if user has admin role.

    Args:
        user_roles: List of user roles

    Returns:
        True if user is admin, False otherwise
    """
    return UserRole.ADMIN in user_roles


def can_access_resource(
    user_roles: list[UserRole], resource_type: str, action: str
) -> bool:
    """
    Check if user can access a specific resource with given action.

    Args:
        user_roles: List of user roles
        resource_type: Type of resource (user, asset, incident, etc.)
        action: Action to perform (create, read, update, delete)

    Returns:
        True if user can access resource, False otherwise
    """
    # Map resource types and actions to permissions
    permission_map = {
        ("user", "create"): Permission.CREATE_USER,
        ("user", "read"): Permission.READ_USER,
        ("user", "update"): Permission.UPDATE_USER,
        ("user", "delete"): Permission.DELETE_USER,
        ("asset", "create"): Permission.CREATE_ASSET,
        ("asset", "read"): Permission.READ_ASSET,
        ("asset", "update"): Permission.UPDATE_ASSET,
        ("asset", "delete"): Permission.DELETE_ASSET,
        ("incident", "create"): Permission.CREATE_INCIDENT,
        ("incident", "read"): Permission.VIEW_SECURITY_EVENTS,
        ("incident", "update"): Permission.UPDATE_INCIDENT,
        ("incident", "delete"): Permission.DELETE_INCIDENT,
        ("attack_path", "read"): Permission.VIEW_ATTACK_PATHS,
        ("attack_path", "create"): Permission.CREATE_ATTACK_SCENARIO,
        ("threat_intel", "read"): Permission.VIEW_THREAT_INTEL,
        ("threat_intel", "create"): Permission.CREATE_IOC,
        ("threat_intel", "update"): Permission.UPDATE_IOC,
        ("dashboard", "read"): Permission.VIEW_DASHBOARD,
        ("dashboard", "create"): Permission.CREATE_DASHBOARD,
        ("dashboard", "update"): Permission.UPDATE_DASHBOARD,
        ("report", "read"): Permission.VIEW_REPORTS,
        ("report", "create"): Permission.CREATE_REPORTS,
    }

    required_permission = permission_map.get((resource_type, action))
    if not required_permission:
        return False

    return has_permission(user_roles, required_permission)
