"""Security Utilities for Authentication and Authorization.

This module provides comprehensive security utilities for the Blast-Radius Security Tool,
including JWT token management, password hashing, secure random generation, and
HTTP security headers.

The module supports:
- JWT access and refresh token creation and verification
- Secure password hashing using bcrypt
- Password reset token generation and validation
- Cryptographically secure random string generation
- API key generation with consistent formatting
- Sensitive data masking for logging and display
- HTTP security headers for enhanced web security

Example:
    Basic token operations:
        >>> access_token = create_access_token("user123")
        >>> user_id = verify_token(access_token)

    Password operations:
        >>> hashed = get_password_hash("secure_password")
        >>> is_valid = verify_password("secure_password", hashed)

    Secure random generation:
        >>> api_key = generate_api_key()
        >>> random_string = generate_secure_random_string(16)

Security Features:
- Uses industry-standard bcrypt for password hashing
- JWT tokens with configurable expiration times
- Cryptographically secure random generation using secrets module
- Comprehensive HTTP security headers following OWASP guidelines
- Sensitive data masking to prevent information leakage

Attributes:
    pwd_context (CryptContext): Password hashing context using bcrypt.
"""

from datetime import datetime, timedelta
import secrets
from typing import Any

from jose import JWTError, jwt
from passlib.context import CryptContext

from app.config import settings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: str | Any, expires_delta: timedelta | None = None
) -> str:
    """
    Create a JWT access token.

    Args:
        subject: The subject (usually user ID) to encode in the token
        expires_delta: Optional custom expiration time

    Returns:
        Encoded JWT token string
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(subject: str | Any) -> str:
    """
    Create a JWT refresh token.

    Args:
        subject: The subject (usually user ID) to encode in the token

    Returns:
        Encoded JWT refresh token string
    """
    expire = datetime.utcnow() + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str) -> str | None:
    """
    Verify and decode a JWT token.

    Args:
        token: JWT token string to verify

    Returns:
        Subject from token if valid, None otherwise
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        subject: str = payload.get("sub")
        if subject is None:
            return None
        return subject
    except JWTError:
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.

    Args:
        plain_password: Plain text password
        hashed_password: Hashed password to verify against

    Returns:
        True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Hash a password.

    Args:
        password: Plain text password to hash

    Returns:
        Hashed password string
    """
    return pwd_context.hash(password)


def generate_password_reset_token(email: str) -> str:
    """
    Generate a password reset token.

    Args:
        email: User email address

    Returns:
        Password reset token
    """
    delta = timedelta(hours=EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> str | None:
    """
    Verify a password reset token.

    Args:
        token: Password reset token to verify

    Returns:
        Email address if token is valid, None otherwise
    """
    try:
        decoded_token = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        return decoded_token["sub"]
    except JWTError:
        return None


def generate_secure_random_string(length: int = 32) -> str:
    """
    Generate a cryptographically secure random string.

    Args:
        length: Length of the random string

    Returns:
        Secure random string
    """
    return secrets.token_urlsafe(length)


def generate_api_key() -> str:
    """
    Generate a secure API key.

    Returns:
        API key string
    """
    return f"br_{generate_secure_random_string(32)}"


def mask_sensitive_data(data: str, visible_chars: int = 4) -> str:
    """
    Mask sensitive data for logging/display.

    Args:
        data: Sensitive data to mask
        visible_chars: Number of characters to keep visible

    Returns:
        Masked string
    """
    if len(data) <= visible_chars:
        return "*" * len(data)

    return data[:visible_chars] + "*" * (len(data) - visible_chars)


class SecurityHeaders:
    """Security headers for HTTP responses.

    This class provides static methods for generating comprehensive
    HTTP security headers that protect against common web vulnerabilities.

    The headers include protection against:
    - Cross-site scripting (XSS) attacks
    - Clickjacking attacks
    - MIME type sniffing
    - Content injection attacks
    - Information leakage through referrer headers

    Example:
        >>> headers = SecurityHeaders.get_security_headers()
        >>> response.headers.update(headers)
    """

    @staticmethod
    def get_security_headers() -> dict:
        """
        Get recommended security headers.

        Returns:
            Dictionary of security headers
        """
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' https:; "
                "connect-src 'self' https:; "
                "frame-ancestors 'none';"
            ),
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            ),
        }


# Password reset token expiration (hours)
EMAIL_RESET_TOKEN_EXPIRE_HOURS = 48
