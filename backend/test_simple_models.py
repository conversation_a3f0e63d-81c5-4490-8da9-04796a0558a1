#!/usr/bin/env python3
"""
Simple model tests without full application setup
"""

import os
import sys
import uuid
from datetime import datetime

# Set environment variables
os.environ['TESTING'] = 'true'
os.environ['SECRET_KEY'] = 'test-secret-key'
os.environ['DATABASE_URL'] = 'sqlite:///./test.db'
os.environ['REDIS_URL'] = 'redis://localhost:6379/1'
os.environ['NEO4J_URL'] = 'bolt://localhost:7687'
os.environ['NEO4J_USER'] = 'neo4j'
os.environ['NEO4J_PASSWORD'] = 'test_password'

def test_cybersecurity_framework_model():
    """Test cybersecurity framework model creation"""
    try:
        from app.db.models.cybersecurity_frameworks import (
            CybersecurityFramework, FrameworkType
        )
        
        # Create a framework instance
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            description="Test framework description",
            authority="Test Authority",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        # Test basic properties
        assert framework.name == "Test Framework"
        assert framework.framework_type == "nist_csf_2_0"
        assert framework.version == "1.0"
        assert framework.description == "Test framework description"
        assert framework.authority == "Test Authority"
        
        print("✓ CybersecurityFramework model test passed")
        return True
        
    except Exception as e:
        print(f"✗ CybersecurityFramework model test failed: {e}")
        return False

def test_framework_control_model():
    """Test framework control model creation"""
    try:
        from app.db.models.cybersecurity_frameworks import (
            FrameworkControl, ControlStatus
        )
        
        # Create a control instance
        control = FrameworkControl(
            framework_id=uuid.uuid4(),
            control_id="TEST-01",
            name="Test Control",
            description="Test control description",
            control_type="preventive",
            automation_level="manual",
            risk_rating="high",
            priority="critical",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        # Test basic properties
        assert control.control_id == "TEST-01"
        assert control.name == "Test Control"
        assert control.control_type == "preventive"
        assert control.risk_rating == "high"
        
        print("✓ FrameworkControl model test passed")
        return True
        
    except Exception as e:
        print(f"✗ FrameworkControl model test failed: {e}")
        return False

def test_control_implementation_model():
    """Test control implementation model creation"""
    try:
        from app.db.models.cybersecurity_frameworks import (
            ControlImplementation, ControlStatus
        )
        
        # Create an implementation instance
        implementation = ControlImplementation(
            control_id=uuid.uuid4(),
            status=ControlStatus.IMPLEMENTED.value,
            implementation_date=datetime.utcnow(),
            responsible_party="Test Team",
            evidence=[{"type": "document", "url": "test.pdf"}],
            gaps=[{"area": "testing", "description": "Need more tests"}],
            implementation_cost=5000.0,
            effort_estimate=40.0,
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        # Test basic properties
        assert implementation.status == "implemented"
        assert implementation.responsible_party == "Test Team"
        assert implementation.implementation_cost == 5000.0
        assert implementation.effort_estimate == 40.0
        assert len(implementation.evidence) == 1
        assert len(implementation.gaps) == 1
        
        print("✓ ControlImplementation model test passed")
        return True
        
    except Exception as e:
        print(f"✗ ControlImplementation model test failed: {e}")
        return False

def test_enum_values():
    """Test enum values"""
    try:
        from app.db.models.cybersecurity_frameworks import (
            FrameworkType, ControlStatus, RiskLevel, AutomationLevel
        )
        
        # Test FrameworkType enum
        assert FrameworkType.NIST_CSF_2_0.value == "nist_csf_2_0"
        assert FrameworkType.ISO_27001_2022.value == "iso_27001_2022"
        assert FrameworkType.CIS_CONTROLS_V8.value == "cis_controls_v8"
        assert FrameworkType.ISF_2022.value == "isf_2022"
        
        # Test ControlStatus enum
        assert ControlStatus.NOT_IMPLEMENTED.value == "not_implemented"
        assert ControlStatus.IMPLEMENTED.value == "implemented"
        assert ControlStatus.VERIFIED.value == "verified"
        
        # Test RiskLevel enum
        assert RiskLevel.LOW.value == "low"
        assert RiskLevel.HIGH.value == "high"
        assert RiskLevel.CRITICAL.value == "critical"
        
        # Test AutomationLevel enum
        assert AutomationLevel.MANUAL.value == "manual"
        assert AutomationLevel.AUTOMATED.value == "automated"
        
        print("✓ Enum values test passed")
        return True
        
    except Exception as e:
        print(f"✗ Enum values test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Running simple model tests...\n")
    
    results = []
    
    print("1. Testing CybersecurityFramework model...")
    results.append(test_cybersecurity_framework_model())
    
    print("\n2. Testing FrameworkControl model...")
    results.append(test_framework_control_model())
    
    print("\n3. Testing ControlImplementation model...")
    results.append(test_control_implementation_model())
    
    print("\n4. Testing enum values...")
    results.append(test_enum_values())
    
    print(f"\n📊 Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("✅ All model tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed")
        sys.exit(1)
