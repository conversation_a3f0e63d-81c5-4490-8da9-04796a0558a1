"""
Simple pytest tests for models only - no database or application setup required
"""

import os
import uuid
import pytest
from datetime import datetime

# Set environment variables before any imports
os.environ['TESTING'] = 'true'
os.environ['SECRET_KEY'] = 'test-secret-key'
os.environ['DATABASE_URL'] = 'sqlite:///./test.db'
os.environ['REDIS_URL'] = 'redis://localhost:6379/1'
os.environ['NEO4J_URL'] = 'bolt://localhost:7687'
os.environ['NEO4J_USER'] = 'neo4j'
os.environ['NEO4J_PASSWORD'] = 'test_password'


class TestCybersecurityFrameworkModels:
    """Test cybersecurity framework models without database"""
    
    def test_framework_creation(self):
        """Test creating a cybersecurity framework"""
        from app.db.models.cybersecurity_frameworks import (
            CybersecurityFramework, FrameworkType
        )
        
        framework = CybersecurityFramework(
            name="Test Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0",
            description="Test framework description",
            authority="Test Authority",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        assert framework.name == "Test Framework"
        assert framework.framework_type == "nist_csf_2_0"
        assert framework.version == "1.0"
        assert framework.description == "Test framework description"
        assert framework.authority == "Test Authority"
    
    def test_framework_control_creation(self):
        """Test creating a framework control"""
        from app.db.models.cybersecurity_frameworks import FrameworkControl
        
        control = FrameworkControl(
            framework_id=uuid.uuid4(),
            control_id="TEST-01",
            name="Test Control",
            description="Test control description",
            control_type="preventive",
            automation_level="manual",
            risk_rating="high",
            priority="critical",
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        assert control.control_id == "TEST-01"
        assert control.name == "Test Control"
        assert control.control_type == "preventive"
        assert control.risk_rating == "high"
    
    def test_control_implementation_creation(self):
        """Test creating a control implementation"""
        from app.db.models.cybersecurity_frameworks import (
            ControlImplementation, ControlStatus
        )
        
        implementation = ControlImplementation(
            control_id=uuid.uuid4(),
            status=ControlStatus.IMPLEMENTED.value,
            implementation_date=datetime.utcnow(),
            responsible_party="Test Team",
            evidence=[{"type": "document", "url": "test.pdf"}],
            gaps=[{"area": "testing", "description": "Need more tests"}],
            implementation_cost=5000.0,
            effort_estimate=40.0,
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        assert implementation.status == "implemented"
        assert implementation.responsible_party == "Test Team"
        assert implementation.implementation_cost == 5000.0
        assert implementation.effort_estimate == 40.0
        assert len(implementation.evidence) == 1
        assert len(implementation.gaps) == 1
    
    def test_framework_enums(self):
        """Test framework enum values"""
        from app.db.models.cybersecurity_frameworks import (
            FrameworkType, ControlStatus, RiskLevel, AutomationLevel
        )
        
        # Test FrameworkType enum
        assert FrameworkType.NIST_CSF_2_0.value == "nist_csf_2_0"
        assert FrameworkType.ISO_27001_2022.value == "iso_27001_2022"
        assert FrameworkType.CIS_CONTROLS_V8.value == "cis_controls_v8"
        assert FrameworkType.ISF_2022.value == "isf_2022"
        
        # Test ControlStatus enum
        assert ControlStatus.NOT_IMPLEMENTED.value == "not_implemented"
        assert ControlStatus.IMPLEMENTED.value == "implemented"
        assert ControlStatus.VERIFIED.value == "verified"
        
        # Test RiskLevel enum
        assert RiskLevel.LOW.value == "low"
        assert RiskLevel.HIGH.value == "high"
        assert RiskLevel.CRITICAL.value == "critical"
        
        # Test AutomationLevel enum
        assert AutomationLevel.MANUAL.value == "manual"
        assert AutomationLevel.AUTOMATED.value == "automated"


class TestConfigurationModels:
    """Test configuration and settings"""
    
    def test_settings_loading(self):
        """Test that settings can be loaded"""
        from app.config import Settings
        
        # Create settings with required fields
        settings = Settings(
            SECRET_KEY="test-secret-key",
            DATABASE_URL="sqlite:///./test.db",
            REDIS_URL="redis://localhost:6379/1",
            NEO4J_URL="bolt://localhost:7687",
            NEO4J_USER="neo4j",
            NEO4J_PASSWORD="test_password"
        )
        
        assert settings.SECRET_KEY == "test-secret-key"
        assert settings.DATABASE_URL == "sqlite:///./test.db"
        assert settings.REDIS_URL == "redis://localhost:6379/1"
        assert settings.NEO4J_URL == "bolt://localhost:7687"
        assert settings.NEO4J_USER == "neo4j"
        assert settings.NEO4J_PASSWORD == "test_password"
    
    def test_cors_origins_default(self):
        """Test CORS origins default value"""
        from app.config import Settings
        
        settings = Settings(
            SECRET_KEY="test-secret-key",
            DATABASE_URL="sqlite:///./test.db",
            REDIS_URL="redis://localhost:6379/1",
            NEO4J_URL="bolt://localhost:7687",
            NEO4J_USER="neo4j",
            NEO4J_PASSWORD="test_password"
        )
        
        assert isinstance(settings.CORS_ORIGINS, list)
        assert len(settings.CORS_ORIGINS) >= 0  # Should have default values


class TestSchemaModels:
    """Test Pydantic schema models"""
    
    def test_cybersecurity_framework_schema(self):
        """Test cybersecurity framework schema"""
        try:
            from app.schemas.cybersecurity_frameworks import CybersecurityFrameworkCreate
            
            schema_data = {
                "name": "Test Framework",
                "framework_type": "nist_csf_2_0",
                "version": "1.0",
                "description": "Test framework description",
                "authority": "Test Authority"
            }
            
            framework_schema = CybersecurityFrameworkCreate(**schema_data)
            
            assert framework_schema.name == "Test Framework"
            assert framework_schema.framework_type == "nist_csf_2_0"
            assert framework_schema.version == "1.0"
            
        except ImportError:
            # Schema might not be available, skip test
            pytest.skip("Cybersecurity framework schemas not available")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
