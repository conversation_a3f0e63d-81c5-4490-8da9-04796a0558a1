"""
Comprehensive documentation testing suite.

This module provides end-to-end testing of all documentation improvements
including build validation, quality metrics, and integration testing.
"""

import json
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List

import pytest


class TestDocumentationComprehensive:
    """Comprehensive documentation testing."""
    
    @pytest.fixture
    def project_root(self) -> Path:
        """Get project root directory."""
        return Path(__file__).parent.parent.parent
    
    @pytest.fixture
    def docs_dir(self, project_root: Path) -> Path:
        """Get documentation directory."""
        return project_root / "docs"
    
    @pytest.fixture
    def backend_dir(self, project_root: Path) -> Path:
        """Get backend directory."""
        return project_root / "backend"
    
    def test_documentation_structure_complete(self, docs_dir: Path):
        """Test that all required documentation files exist."""
        required_files = [
            "index.rst",
            "installation.rst", 
            "quick-start-guide.rst",
            "configuration.rst",
            "api/index.rst",
            "api/authentication.rst",
            "api/attack-path-analysis.rst",
            "api/asset-management.rst",
            "user-guides/index.rst",
            "user-guides/soc-operators.rst",
            "user-guides/security-architects.rst",
            "user-guides/red-team-members.rst",
            "user-guides/purple-team-members.rst",
            "developer-guide/documentation-standards.rst",
            "troubleshooting/faq.rst",
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = docs_dir / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        assert not missing_files, f"Missing required documentation files: {missing_files}"
    
    def test_sphinx_configuration_enhanced(self, docs_dir: Path):
        """Test that Sphinx configuration includes all enhanced extensions."""
        conf_file = docs_dir / "conf.py"
        assert conf_file.exists(), "Sphinx configuration file missing"
        
        content = conf_file.read_text()
        
        # Check for enhanced extensions
        required_extensions = [
            "sphinx.ext.autodoc",
            "sphinx.ext.autosummary", 
            "sphinx.ext.viewcode",
            "sphinx.ext.napoleon",
            "sphinx.ext.doctest",
            "sphinx.ext.coverage",
            "sphinx_copybutton",
            "sphinx_design",
            "autoapi.extension",
            "myst_parser",
        ]
        
        for extension in required_extensions:
            assert extension in content, f"Missing Sphinx extension: {extension}"
    
    def test_documentation_builds_successfully(self, docs_dir: Path):
        """Test that documentation builds without errors."""
        build_dir = docs_dir / "_build" / "test_comprehensive"
        
        cmd = [
            "sphinx-build",
            "-b", "html",
            "-W",  # Treat warnings as errors
            "-E",  # Don't use saved environment
            str(docs_dir),
            str(build_dir)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        assert result.returncode == 0, f"Documentation build failed:\n{result.stderr}"
        assert (build_dir / "index.html").exists(), "HTML index file not generated"
    
    def test_documentation_tests_exist(self, backend_dir: Path):
        """Test that all documentation test files exist."""
        test_files = [
            "tests/test_documentation.py",
            "tests/test_docs_integration.py", 
            "tests/test_docs_comprehensive.py",
            "tests/conftest_docs.py",
        ]
        
        for test_file in test_files:
            file_path = backend_dir / test_file
            assert file_path.exists(), f"Documentation test file missing: {test_file}"
    
    def test_documentation_scripts_exist(self, backend_dir: Path):
        """Test that documentation utility scripts exist."""
        script_files = [
            "scripts/docs_quality_check.py",
            "scripts/run_docs_tests.py",
        ]
        
        for script_file in script_files:
            file_path = backend_dir / script_file
            assert file_path.exists(), f"Documentation script missing: {script_file}"
            
            # Check that script is executable
            content = file_path.read_text()
            assert content.startswith("#!/usr/bin/env python3"), f"Script not executable: {script_file}"
    
    def test_makefile_documentation_targets(self, backend_dir: Path):
        """Test that Makefile includes all documentation targets."""
        makefile = backend_dir / "Makefile"
        assert makefile.exists(), "Makefile missing"
        
        content = makefile.read_text()
        
        required_targets = [
            "docs-build",
            "docs-serve",
            "docs-clean",
            "docs-linkcheck",
            "docs-coverage",
            "docs-doctest",
            "docs-quality",
            "test-docs",
            "test-docs-build",
            "test-docs-coverage",
            "test-docs-integration",
        ]
        
        for target in required_targets:
            assert f"{target}:" in content, f"Missing Makefile target: {target}"
    
    def test_pyproject_documentation_dependencies(self, backend_dir: Path):
        """Test that pyproject.toml includes enhanced documentation dependencies."""
        pyproject_file = backend_dir / "pyproject.toml"
        assert pyproject_file.exists(), "pyproject.toml missing"
        
        content = pyproject_file.read_text()
        
        required_deps = [
            "sphinx>=7.2.6",
            "sphinx-rtd-theme",
            "sphinx-autodoc-typehints",
            "sphinx-copybutton",
            "sphinx-design",
            "sphinx-autoapi",
            "myst-parser",
            "beautifulsoup4",
        ]
        
        for dep in required_deps:
            assert dep in content, f"Missing documentation dependency: {dep}"
    
    def test_documentation_quality_script_works(self, backend_dir: Path):
        """Test that documentation quality checker script works."""
        script_path = backend_dir / "scripts" / "docs_quality_check.py"
        
        # Run quality check with dry-run
        cmd = [
            sys.executable,
            str(script_path),
            "--project-root", str(backend_dir.parent),
            "--fail-threshold", "0"  # Don't fail on low scores for this test
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        # Script should run without errors (may have quality issues but shouldn't crash)
        assert result.returncode in [0, 1], f"Quality check script failed: {result.stderr}"
        assert "Documentation Quality Report" in result.stdout, "Quality report not generated"
    
    def test_documentation_test_runner_works(self, backend_dir: Path):
        """Test that documentation test runner script works."""
        script_path = backend_dir / "scripts" / "run_docs_tests.py"
        
        # Run a single test suite to verify script works
        cmd = [
            sys.executable,
            str(script_path),
            "--project-root", str(backend_dir.parent),
            "--suite", "build"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        # Script should run without errors
        assert result.returncode in [0, 1], f"Test runner script failed: {result.stderr}"
        assert "Documentation Testing Report" in result.stdout, "Test report not generated"
    
    def test_api_documentation_comprehensive(self, docs_dir: Path):
        """Test that API documentation is comprehensive."""
        api_dir = docs_dir / "api"
        
        # Check that API documentation files exist
        api_files = [
            "index.rst",
            "authentication.rst",
            "attack-path-analysis.rst", 
            "asset-management.rst",
        ]
        
        for api_file in api_files:
            file_path = api_dir / api_file
            assert file_path.exists(), f"API documentation file missing: {api_file}"
            
            # Check that file has substantial content
            content = file_path.read_text()
            assert len(content) > 1000, f"API documentation file too short: {api_file}"
            
            # Check for required sections
            assert ".. http::" in content or ".. code-block::" in content, \
                f"API file missing HTTP examples: {api_file}"
    
    def test_documentation_standards_exist(self, docs_dir: Path):
        """Test that documentation standards are documented."""
        standards_file = docs_dir / "developer-guide" / "documentation-standards.rst"
        assert standards_file.exists(), "Documentation standards file missing"
        
        content = standards_file.read_text()
        
        # Check for key sections
        required_sections = [
            "Docstring Standards",
            "RST Documentation Standards", 
            "API Documentation Standards",
            "Testing Documentation",
            "Quality Metrics",
        ]
        
        for section in required_sections:
            assert section in content, f"Missing documentation standards section: {section}"
    
    def test_pytest_docs_configuration(self, backend_dir: Path):
        """Test that pytest documentation configuration exists."""
        pytest_docs_file = backend_dir / "pytest-docs.ini"
        assert pytest_docs_file.exists(), "pytest-docs.ini configuration missing"
        
        content = pytest_docs_file.read_text()
        
        # Check for documentation-specific markers
        required_markers = [
            "docs:",
            "docs_build:",
            "docs_quality:",
            "docs_integration:",
        ]
        
        for marker in required_markers:
            assert marker in content, f"Missing pytest marker: {marker}"
    
    def test_documentation_ci_integration(self, backend_dir: Path):
        """Test that documentation is integrated into CI/CD."""
        makefile = backend_dir / "Makefile"
        content = makefile.read_text()
        
        # Check that ci-full target includes documentation
        assert "docs-all" in content, "Documentation not included in CI pipeline"
        assert "test-docs" in content, "Documentation tests not included in CI"
    
    @pytest.mark.slow
    def test_full_documentation_pipeline(self, project_root: Path):
        """Test the complete documentation pipeline end-to-end."""
        backend_dir = project_root / "backend"
        
        # Run the full documentation pipeline
        cmd = [
            "make", "-C", str(backend_dir),
            "docs-all"
        ]
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        duration = time.time() - start_time
        
        # Pipeline should complete successfully
        assert result.returncode == 0, f"Documentation pipeline failed:\n{result.stderr}"
        
        # Should complete in reasonable time (10 minutes max)
        assert duration < 600, f"Documentation pipeline too slow: {duration:.2f}s"
        
        # Check that all outputs were generated
        docs_dir = project_root / "docs"
        build_dir = docs_dir / "_build"
        
        expected_outputs = [
            "html/index.html",
            "linkcheck/output.txt",
            "coverage/python.txt",
        ]
        
        for output in expected_outputs:
            output_path = build_dir / output
            assert output_path.exists(), f"Expected documentation output missing: {output}"
    
    def test_documentation_performance_benchmarks(self, docs_dir: Path):
        """Test that documentation meets performance benchmarks."""
        build_dir = docs_dir / "_build" / "performance_test"
        
        # Time the build
        start_time = time.time()
        
        cmd = [
            "sphinx-build",
            "-b", "html",
            "-E",
            str(docs_dir),
            str(build_dir)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        build_time = time.time() - start_time
        
        assert result.returncode == 0, "Documentation build failed"
        
        # Build should complete within 2 minutes
        assert build_time < 120, f"Documentation build too slow: {build_time:.2f}s"
        
        # Check output size is reasonable
        if build_dir.exists():
            total_size = sum(f.stat().st_size for f in build_dir.rglob('*') if f.is_file())
            total_size_mb = total_size / (1024 * 1024)
            
            # Documentation should be under 50MB
            assert total_size_mb < 50, f"Documentation too large: {total_size_mb:.2f}MB"
    
    def test_documentation_accessibility_basics(self, docs_dir: Path):
        """Test basic documentation accessibility requirements."""
        build_dir = docs_dir / "_build" / "accessibility_test"
        
        # Build documentation
        cmd = [
            "sphinx-build",
            "-b", "html",
            str(docs_dir),
            str(build_dir)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        assert result.returncode == 0, "Documentation build failed"
        
        # Check index.html for basic accessibility
        index_file = build_dir / "index.html"
        if index_file.exists():
            content = index_file.read_text()
            
            # Check for viewport meta tag
            assert 'name="viewport"' in content, "Missing viewport meta tag"
            
            # Check for language attribute
            assert 'lang=' in content, "Missing language attribute"
            
            # Check for proper heading structure
            assert '<h1' in content, "Missing main heading"


class TestDocumentationMetrics:
    """Test documentation quality metrics."""
    
    def test_documentation_coverage_metrics(self, backend_dir: Path):
        """Test that documentation coverage can be measured."""
        from backend.scripts.docs_quality_check import DocstringAnalyzer
        
        app_dir = backend_dir / "app"
        analyzer = DocstringAnalyzer(app_dir)
        
        coverage, issues = analyzer.analyze_all()
        
        # Should be able to analyze coverage
        assert isinstance(coverage, float), "Coverage should be a float"
        assert 0 <= coverage <= 1, "Coverage should be between 0 and 1"
        assert isinstance(issues, list), "Issues should be a list"
    
    def test_documentation_quality_scoring(self):
        """Test that documentation quality can be scored."""
        from backend.scripts.docs_quality_check import QualityMetrics
        
        # Create sample metrics
        metrics = QualityMetrics(
            docstring_coverage=0.85,
            build_success=True,
            build_time=30.0,
            broken_links=2,
            total_links=50,
            content_issues=["Minor issue"],
            performance_score=85.0,
            overall_score=82.5
        )
        
        # Verify metrics are reasonable
        assert 0 <= metrics.overall_score <= 100, "Overall score should be 0-100"
        assert 0 <= metrics.performance_score <= 100, "Performance score should be 0-100"
        assert metrics.docstring_coverage >= 0, "Coverage should be non-negative"
