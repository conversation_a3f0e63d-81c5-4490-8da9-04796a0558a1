"""
Comprehensive edge case tests for error handling, boundary conditions, and complex scenarios
"""

import os
import uuid
import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Set environment variables before any imports
os.environ['TESTING'] = 'true'
os.environ['SECRET_KEY'] = 'test-secret-key'
os.environ['DATABASE_URL'] = 'sqlite:///./test.db'
os.environ['REDIS_URL'] = 'redis://localhost:6379/1'
os.environ['NEO4J_URL'] = 'bolt://localhost:7687'
os.environ['NEO4J_USER'] = 'neo4j'
os.environ['NEO4J_PASSWORD'] = 'test_password'


class TestConfigurationValidationEdgeCases:
    """Test configuration validation edge cases and error conditions"""
    
    def test_secret_key_validation_empty(self):
        """Test SECRET_KEY validation with empty value"""
        from app.config import Settings
        
        with pytest.raises(ValueError, match="SECRET_KEY must be provided"):
            Settings(
                SECRET_KEY="",
                DATABASE_URL="sqlite:///./test.db",
                REDIS_URL="redis://localhost:6379/1",
                NEO4J_URL="bolt://localhost:7687",
                NEO4J_USER="neo4j",
                NEO4J_PASSWORD="test_password"
            )
    
    def test_secret_key_validation_too_short_production(self):
        """Test SECRET_KEY validation with short key in production"""
        from app.config import Settings
        
        # Temporarily unset TESTING to simulate production
        with patch.dict(os.environ, {'TESTING': ''}, clear=False):
            with pytest.raises(ValueError, match="SECRET_KEY must be at least 32 characters long"):
                Settings(
                    SECRET_KEY="short",
                    DATABASE_URL="sqlite:///./test.db",
                    REDIS_URL="redis://localhost:6379/1",
                    NEO4J_URL="bolt://localhost:7687",
                    NEO4J_USER="neo4j",
                    NEO4J_PASSWORD="test_password"
                )
    
    def test_database_url_validation_empty(self):
        """Test DATABASE_URL validation with empty value"""
        from app.config import Settings
        
        with pytest.raises(ValueError, match="DATABASE_URL must be provided"):
            Settings(
                SECRET_KEY="test-secret-key",
                DATABASE_URL="",
                REDIS_URL="redis://localhost:6379/1",
                NEO4J_URL="bolt://localhost:7687",
                NEO4J_USER="neo4j",
                NEO4J_PASSWORD="test_password"
            )
    
    def test_database_url_validation_invalid_scheme(self):
        """Test DATABASE_URL validation with invalid scheme"""
        from app.config import Settings
        
        with pytest.raises(ValueError, match="DATABASE_URL must be a PostgreSQL or SQLite URL"):
            Settings(
                SECRET_KEY="test-secret-key",
                DATABASE_URL="mysql://user:pass@localhost/db",
                REDIS_URL="redis://localhost:6379/1",
                NEO4J_URL="bolt://localhost:7687",
                NEO4J_USER="neo4j",
                NEO4J_PASSWORD="test_password"
            )
    
    def test_redis_url_validation_empty(self):
        """Test REDIS_URL validation with empty value"""
        from app.config import Settings
        
        with pytest.raises(ValueError, match="REDIS_URL must be provided"):
            Settings(
                SECRET_KEY="test-secret-key",
                DATABASE_URL="sqlite:///./test.db",
                REDIS_URL="",
                NEO4J_URL="bolt://localhost:7687",
                NEO4J_USER="neo4j",
                NEO4J_PASSWORD="test_password"
            )
    
    def test_redis_url_validation_invalid_scheme(self):
        """Test REDIS_URL validation with invalid scheme"""
        from app.config import Settings
        
        with pytest.raises(ValueError, match="REDIS_URL must be a Redis URL"):
            Settings(
                SECRET_KEY="test-secret-key",
                DATABASE_URL="sqlite:///./test.db",
                REDIS_URL="http://localhost:6379",
                NEO4J_URL="bolt://localhost:7687",
                NEO4J_USER="neo4j",
                NEO4J_PASSWORD="test_password"
            )
    
    def test_neo4j_url_validation_empty(self):
        """Test NEO4J_URL validation with empty value"""
        from app.config import Settings
        
        with pytest.raises(ValueError, match="NEO4J_URL must be provided"):
            Settings(
                SECRET_KEY="test-secret-key",
                DATABASE_URL="sqlite:///./test.db",
                REDIS_URL="redis://localhost:6379/1",
                NEO4J_URL="",
                NEO4J_USER="neo4j",
                NEO4J_PASSWORD="test_password"
            )
    
    def test_neo4j_url_validation_invalid_scheme(self):
        """Test NEO4J_URL validation with invalid scheme"""
        from app.config import Settings
        
        with pytest.raises(ValueError, match="NEO4J_URL must be a valid Neo4j URL"):
            Settings(
                SECRET_KEY="test-secret-key",
                DATABASE_URL="sqlite:///./test.db",
                REDIS_URL="redis://localhost:6379/1",
                NEO4J_URL="http://localhost:7687",
                NEO4J_USER="neo4j",
                NEO4J_PASSWORD="test_password"
            )
    
    def test_get_database_url_with_test_url(self):
        """Test get_database_url function with test URL set"""
        from app.config import Settings, get_database_url
        
        # Create settings with test URL
        settings = Settings(
            SECRET_KEY="test-secret-key",
            DATABASE_URL="sqlite:///./prod.db",
            DATABASE_TEST_URL="sqlite:///./test.db",
            REDIS_URL="redis://localhost:6379/1",
            NEO4J_URL="bolt://localhost:7687",
            NEO4J_USER="neo4j",
            NEO4J_PASSWORD="test_password",
            TESTING=True
        )
        
        # Mock the global settings
        with patch('app.config.settings', settings):
            result = get_database_url()
            assert result == "sqlite:///./test.db"
    
    def test_get_redis_url_with_test_url(self):
        """Test get_redis_url function with test URL set"""
        from app.config import Settings, get_redis_url
        
        # Create settings with test URL
        settings = Settings(
            SECRET_KEY="test-secret-key",
            DATABASE_URL="sqlite:///./test.db",
            REDIS_URL="redis://localhost:6379/0",
            REDIS_TEST_URL="redis://localhost:6379/1",
            NEO4J_URL="bolt://localhost:7687",
            NEO4J_USER="neo4j",
            NEO4J_PASSWORD="test_password",
            TESTING=True
        )
        
        # Mock the global settings
        with patch('app.config.settings', settings):
            result = get_redis_url()
            assert result == "redis://localhost:6379/1"


class TestCybersecurityFrameworkEdgeCases:
    """Test cybersecurity framework models edge cases"""
    
    def test_framework_with_minimal_data(self):
        """Test framework creation with minimal required data"""
        from app.db.models.cybersecurity_frameworks import (
            CybersecurityFramework, FrameworkType
        )
        
        framework = CybersecurityFramework(
            name="Minimal Framework",
            framework_type=FrameworkType.NIST_CSF_2_0.value,
            version="1.0"
        )
        
        assert framework.name == "Minimal Framework"
        assert framework.framework_type == "nist_csf_2_0"
        assert framework.version == "1.0"
        assert framework.description is None
        assert framework.authority is None
    
    def test_framework_with_maximum_data(self):
        """Test framework creation with all possible data"""
        from app.db.models.cybersecurity_frameworks import (
            CybersecurityFramework, FrameworkType
        )
        
        framework = CybersecurityFramework(
            name="Maximum Framework",
            framework_type=FrameworkType.ISO_27001_2022.value,
            version="2.0",
            description="A very detailed description of the framework",
            authority="International Organization for Standardization",
            publication_date=datetime(2022, 10, 25),
            effective_date=datetime(2023, 1, 1),
            status="active",
            framework_metadata={"custom_field": "custom_value", "tags": ["security", "compliance"]},
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        assert framework.name == "Maximum Framework"
        assert framework.framework_type == "iso_27001_2022"
        assert framework.version == "2.0"
        assert framework.description == "A very detailed description of the framework"
        assert framework.authority == "International Organization for Standardization"
        assert framework.publication_date == datetime(2022, 10, 25)
        assert framework.effective_date == datetime(2023, 1, 1)
        assert framework.status == "active"
        assert framework.framework_metadata["custom_field"] == "custom_value"
        assert "security" in framework.framework_metadata["tags"]
    
    def test_control_with_complex_metadata(self):
        """Test control creation with complex metadata structures"""
        from app.db.models.cybersecurity_frameworks import FrameworkControl
        
        complex_metadata = {
            "assessment_criteria": [
                {"criterion": "Policy exists", "weight": 0.3},
                {"criterion": "Policy is current", "weight": 0.2},
                {"criterion": "Policy is communicated", "weight": 0.5}
            ],
            "testing_procedures": [
                {"step": 1, "action": "Review policy document"},
                {"step": 2, "action": "Interview staff"},
                {"step": 3, "action": "Test implementation"}
            ],
            "evidence_requirements": {
                "documents": ["policy", "procedures", "training_records"],
                "interviews": ["security_officer", "staff_sample"],
                "technical_tests": ["access_controls", "monitoring"]
            }
        }
        
        control = FrameworkControl(
            framework_id=uuid.uuid4(),
            control_id="COMPLEX-01",
            name="Complex Control",
            description="A control with complex metadata",
            assessment_criteria=complex_metadata["assessment_criteria"],
            testing_procedures=complex_metadata["testing_procedures"],
            evidence_requirements=complex_metadata["evidence_requirements"],
            references=[{"standard": "ISO 27001", "section": "A.5.1.1"}],
            tags=["policy", "governance", "critical"],
            control_metadata={"complexity": "high", "automation_potential": "low"},
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        assert control.control_id == "COMPLEX-01"
        assert len(control.assessment_criteria) == 3
        assert len(control.testing_procedures) == 3
        assert "documents" in control.evidence_requirements
        assert control.references[0]["standard"] == "ISO 27001"
        assert "policy" in control.tags
    
    def test_implementation_with_gaps_and_evidence(self):
        """Test implementation with detailed gaps and evidence"""
        from app.db.models.cybersecurity_frameworks import (
            ControlImplementation, ControlStatus
        )
        
        detailed_evidence = [
            {"type": "document", "name": "Security Policy v2.1", "url": "/docs/policy.pdf", "date": "2024-01-15"},
            {"type": "screenshot", "name": "Access Control Settings", "url": "/evidence/screenshot1.png", "date": "2024-01-20"},
            {"type": "interview", "name": "Security Officer Interview", "summary": "Confirmed policy awareness", "date": "2024-01-18"}
        ]
        
        identified_gaps = [
            {"area": "training", "description": "Staff training not documented", "priority": "high", "estimated_effort": 16},
            {"area": "monitoring", "description": "Automated monitoring not implemented", "priority": "medium", "estimated_effort": 40}
        ]
        
        implementation = ControlImplementation(
            control_id=uuid.uuid4(),
            status=ControlStatus.PARTIALLY_IMPLEMENTED.value,
            implementation_date=datetime.utcnow() - timedelta(days=30),
            target_completion_date=datetime.utcnow() + timedelta(days=60),
            responsible_party="Security Team",
            evidence=detailed_evidence,
            gaps=identified_gaps,
            remediation_plan="1. Develop training program 2. Implement monitoring solution",
            remediation_priority="high",
            implementation_cost=15000.0,
            effort_estimate=80.0,
            created_by=uuid.uuid4(),
            updated_by=uuid.uuid4()
        )
        
        assert implementation.status == "partially_implemented"
        assert len(implementation.evidence) == 3
        assert len(implementation.gaps) == 2
        assert implementation.evidence[0]["type"] == "document"
        assert implementation.gaps[0]["area"] == "training"
        assert implementation.implementation_cost == 15000.0
        assert implementation.effort_estimate == 80.0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
