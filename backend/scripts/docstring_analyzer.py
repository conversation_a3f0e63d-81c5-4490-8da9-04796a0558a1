#!/usr/bin/env python3
"""
Comprehensive docstring analysis and reporting tool for Blast-Radius Security Tool.

This tool provides detailed analysis of docstring coverage, quality assessment,
and progress tracking across the entire Python codebase.
"""

import ast
import json
import re
import sys
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union
import argparse
from datetime import datetime


@dataclass
class DocstringInfo:
    """Information about a single docstring."""
    content: str
    line_number: int
    has_args: bool
    has_returns: bool
    has_raises: bool
    has_examples: bool
    word_count: int
    quality_score: float


@dataclass
class CodeElementInfo:
    """Information about a code element (function, class, module)."""
    name: str
    type: str  # 'function', 'class', 'module', 'method'
    line_number: int
    has_docstring: bool
    docstring_info: Optional[DocstringInfo]
    is_public: bool
    is_property: bool
    is_async: bool
    parameters: List[str]
    return_annotation: Optional[str]


@dataclass
class FileAnalysis:
    """Analysis results for a single Python file."""
    file_path: str
    total_elements: int
    documented_elements: int
    coverage_percentage: float
    quality_score: float
    elements: List[CodeElementInfo]
    issues: List[str]
    module_docstring: Optional[DocstringInfo]


@dataclass
class ProjectAnalysis:
    """Complete project analysis results."""
    total_files: int
    analyzed_files: int
    total_elements: int
    documented_elements: int
    overall_coverage: float
    overall_quality: float
    file_analyses: List[FileAnalysis]
    summary_by_category: Dict[str, Dict[str, Union[int, float]]]
    top_issues: List[str]


class DocstringAnalyzer:
    """Analyzes Python files for docstring coverage and quality."""
    
    def __init__(self, project_root: Path):
        """Initialize the docstring analyzer.
        
        Args:
            project_root: Root directory of the project to analyze.
        """
        self.project_root = project_root
        self.backend_dir = project_root / "backend"
        self.app_dir = self.backend_dir / "app"
        
        # Quality criteria weights
        self.quality_weights = {
            'has_description': 0.3,
            'has_args': 0.2,
            'has_returns': 0.2,
            'has_raises': 0.1,
            'has_examples': 0.1,
            'length_adequate': 0.1
        }
    
    def get_python_files(self) -> List[Path]:
        """Get all Python files in the project.
        
        Returns:
            List of Python file paths to analyze.
        """
        python_files = []
        
        # Get all Python files in backend/app
        if self.app_dir.exists():
            python_files.extend(self.app_dir.rglob("*.py"))
        
        # Get test files
        test_dir = self.backend_dir / "tests"
        if test_dir.exists():
            python_files.extend(test_dir.rglob("*.py"))
        
        # Get script files
        scripts_dir = self.backend_dir / "scripts"
        if scripts_dir.exists():
            python_files.extend(scripts_dir.rglob("*.py"))
        
        # Filter out __pycache__ and other unwanted files
        filtered_files = []
        for file_path in python_files:
            if "__pycache__" not in str(file_path) and file_path.name != "__pycache__":
                filtered_files.append(file_path)
        
        return sorted(filtered_files)
    
    def analyze_docstring(self, docstring: str, context: str = "") -> DocstringInfo:
        """Analyze the quality of a docstring.
        
        Args:
            docstring: The docstring content to analyze.
            context: Context about where the docstring is used.
            
        Returns:
            DocstringInfo object with analysis results.
        """
        if not docstring:
            return DocstringInfo(
                content="",
                line_number=0,
                has_args=False,
                has_returns=False,
                has_raises=False,
                has_examples=False,
                word_count=0,
                quality_score=0.0
            )
        
        # Basic metrics
        word_count = len(docstring.split())
        
        # Check for standard sections
        has_args = bool(re.search(r'\b(Args?|Arguments?|Parameters?):', docstring, re.IGNORECASE))
        has_returns = bool(re.search(r'\b(Returns?|Return):', docstring, re.IGNORECASE))
        has_raises = bool(re.search(r'\b(Raises?|Raise):', docstring, re.IGNORECASE))
        has_examples = bool(re.search(r'\b(Examples?|Example):', docstring, re.IGNORECASE))
        
        # Calculate quality score
        quality_score = self._calculate_quality_score(
            docstring, has_args, has_returns, has_raises, has_examples, word_count
        )
        
        return DocstringInfo(
            content=docstring,
            line_number=0,  # Will be set by caller
            has_args=has_args,
            has_returns=has_returns,
            has_raises=has_raises,
            has_examples=has_examples,
            word_count=word_count,
            quality_score=quality_score
        )
    
    def _calculate_quality_score(self, docstring: str, has_args: bool, 
                                has_returns: bool, has_raises: bool, 
                                has_examples: bool, word_count: int) -> float:
        """Calculate quality score for a docstring.
        
        Args:
            docstring: The docstring content.
            has_args: Whether docstring has Args section.
            has_returns: Whether docstring has Returns section.
            has_raises: Whether docstring has Raises section.
            has_examples: Whether docstring has Examples section.
            word_count: Number of words in docstring.
            
        Returns:
            Quality score between 0.0 and 1.0.
        """
        score = 0.0
        
        # Has meaningful description (not just one word)
        if word_count >= 3:
            score += self.quality_weights['has_description']
        
        # Has proper sections
        if has_args:
            score += self.quality_weights['has_args']
        if has_returns:
            score += self.quality_weights['has_returns']
        if has_raises:
            score += self.quality_weights['has_raises']
        if has_examples:
            score += self.quality_weights['has_examples']
        
        # Adequate length (at least 10 words for meaningful documentation)
        if word_count >= 10:
            score += self.quality_weights['length_adequate']
        
        return min(score, 1.0)
    
    def analyze_file(self, file_path: Path) -> FileAnalysis:
        """Analyze a single Python file for docstring coverage.
        
        Args:
            file_path: Path to the Python file to analyze.
            
        Returns:
            FileAnalysis object with results.
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            elements = []
            issues = []
            module_docstring = None
            
            # Check for module docstring
            if (tree.body and isinstance(tree.body[0], ast.Expr) and 
                isinstance(tree.body[0].value, ast.Constant) and 
                isinstance(tree.body[0].value.value, str)):
                
                module_docstring = self.analyze_docstring(
                    tree.body[0].value.value, f"module {file_path.name}"
                )
                module_docstring.line_number = tree.body[0].lineno
            else:
                issues.append(f"Missing module docstring in {file_path.name}")
            
            # Analyze all functions and classes
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                    element_info = self._analyze_code_element(node, file_path)
                    elements.append(element_info)
                    
                    if element_info.is_public and not element_info.has_docstring:
                        issues.append(
                            f"Missing docstring for {element_info.type} "
                            f"'{element_info.name}' at line {element_info.line_number}"
                        )
            
            # Calculate coverage
            total_elements = len(elements)
            documented_elements = sum(1 for e in elements if e.has_docstring)
            coverage_percentage = (documented_elements / total_elements * 100) if total_elements > 0 else 0
            
            # Calculate average quality score
            quality_scores = [e.docstring_info.quality_score for e in elements 
                            if e.docstring_info is not None]
            if module_docstring:
                quality_scores.append(module_docstring.quality_score)
            
            quality_score = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            
            return FileAnalysis(
                file_path=str(file_path.relative_to(self.project_root)),
                total_elements=total_elements,
                documented_elements=documented_elements,
                coverage_percentage=coverage_percentage,
                quality_score=quality_score,
                elements=elements,
                issues=issues,
                module_docstring=module_docstring
            )
            
        except Exception as e:
            return FileAnalysis(
                file_path=str(file_path.relative_to(self.project_root)),
                total_elements=0,
                documented_elements=0,
                coverage_percentage=0,
                quality_score=0,
                elements=[],
                issues=[f"Error analyzing file: {str(e)}"],
                module_docstring=None
            )
    
    def _analyze_code_element(self, node: ast.AST, file_path: Path) -> CodeElementInfo:
        """Analyze a single code element (function, class, etc.).
        
        Args:
            node: AST node to analyze.
            file_path: Path to the file containing the node.
            
        Returns:
            CodeElementInfo object with analysis results.
        """
        name = node.name
        is_public = not name.startswith('_')
        is_property = False
        is_async = isinstance(node, ast.AsyncFunctionDef)
        parameters = []
        return_annotation = None
        
        # Determine element type
        if isinstance(node, ast.ClassDef):
            element_type = "class"
        elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            element_type = "method" if self._is_method(node) else "function"
            
            # Check if it's a property
            for decorator in node.decorator_list:
                if (isinstance(decorator, ast.Name) and decorator.id == 'property') or \
                   (isinstance(decorator, ast.Attribute) and decorator.attr == 'property'):
                    is_property = True
                    break
            
            # Get parameters
            parameters = [arg.arg for arg in node.args.args]
            
            # Get return annotation
            if node.returns:
                return_annotation = ast.unparse(node.returns) if hasattr(ast, 'unparse') else str(node.returns)
        else:
            element_type = "unknown"
        
        # Check for docstring
        has_docstring = False
        docstring_info = None
        
        if (node.body and isinstance(node.body[0], ast.Expr) and 
            isinstance(node.body[0].value, ast.Constant) and 
            isinstance(node.body[0].value.value, str)):
            
            has_docstring = True
            docstring_content = node.body[0].value.value
            docstring_info = self.analyze_docstring(docstring_content, f"{element_type} {name}")
            docstring_info.line_number = node.body[0].lineno
        
        return CodeElementInfo(
            name=name,
            type=element_type,
            line_number=node.lineno,
            has_docstring=has_docstring,
            docstring_info=docstring_info,
            is_public=is_public,
            is_property=is_property,
            is_async=is_async,
            parameters=parameters,
            return_annotation=return_annotation
        )
    
    def _is_method(self, node: ast.FunctionDef) -> bool:
        """Check if a function node is a method (inside a class).
        
        Args:
            node: Function AST node to check.
            
        Returns:
            True if the function is a method, False otherwise.
        """
        # This is a simplified check - in practice, you'd need to track
        # the AST context to determine if we're inside a class
        return len(node.args.args) > 0 and node.args.args[0].arg in ('self', 'cls')
    
    def analyze_project(self) -> ProjectAnalysis:
        """Analyze the entire project for docstring coverage.
        
        Returns:
            ProjectAnalysis object with complete results.
        """
        python_files = self.get_python_files()
        file_analyses = []
        
        print(f"Analyzing {len(python_files)} Python files...")
        
        for file_path in python_files:
            print(f"  Analyzing {file_path.relative_to(self.project_root)}...")
            analysis = self.analyze_file(file_path)
            file_analyses.append(analysis)
        
        # Calculate overall statistics
        total_files = len(python_files)
        analyzed_files = len([a for a in file_analyses if a.total_elements > 0])
        total_elements = sum(a.total_elements for a in file_analyses)
        documented_elements = sum(a.documented_elements for a in file_analyses)
        
        overall_coverage = (documented_elements / total_elements * 100) if total_elements > 0 else 0
        overall_quality = sum(a.quality_score for a in file_analyses) / len(file_analyses) if file_analyses else 0
        
        # Categorize results
        summary_by_category = self._categorize_results(file_analyses)
        
        # Collect top issues
        all_issues = []
        for analysis in file_analyses:
            all_issues.extend(analysis.issues)
        top_issues = all_issues[:20]  # Top 20 issues
        
        return ProjectAnalysis(
            total_files=total_files,
            analyzed_files=analyzed_files,
            total_elements=total_elements,
            documented_elements=documented_elements,
            overall_coverage=overall_coverage,
            overall_quality=overall_quality,
            file_analyses=file_analyses,
            summary_by_category=summary_by_category,
            top_issues=top_issues
        )
    
    def _categorize_results(self, file_analyses: List[FileAnalysis]) -> Dict[str, Dict[str, Union[int, float]]]:
        """Categorize analysis results by file type and priority.
        
        Args:
            file_analyses: List of file analysis results.
            
        Returns:
            Dictionary with categorized statistics.
        """
        categories = {
            'api_files': [],
            'core_files': [],
            'service_files': [],
            'model_files': [],
            'test_files': [],
            'other_files': []
        }
        
        for analysis in file_analyses:
            file_path = analysis.file_path
            
            if '/api/' in file_path:
                categories['api_files'].append(analysis)
            elif '/core/' in file_path:
                categories['core_files'].append(analysis)
            elif '/services/' in file_path:
                categories['service_files'].append(analysis)
            elif '/models/' in file_path:
                categories['model_files'].append(analysis)
            elif '/tests/' in file_path:
                categories['test_files'].append(analysis)
            else:
                categories['other_files'].append(analysis)
        
        summary = {}
        for category, analyses in categories.items():
            if analyses:
                total_elements = sum(a.total_elements for a in analyses)
                documented_elements = sum(a.documented_elements for a in analyses)
                coverage = (documented_elements / total_elements * 100) if total_elements > 0 else 0
                quality = sum(a.quality_score for a in analyses) / len(analyses)
                
                summary[category] = {
                    'files': len(analyses),
                    'total_elements': total_elements,
                    'documented_elements': documented_elements,
                    'coverage': coverage,
                    'quality': quality
                }
        
        return summary
    
    def generate_report(self, analysis: ProjectAnalysis, output_file: Optional[Path] = None) -> str:
        """Generate a comprehensive analysis report.
        
        Args:
            analysis: Project analysis results.
            output_file: Optional file to save the report.
            
        Returns:
            Report content as string.
        """
        report = f"""
📊 Comprehensive Docstring Analysis Report
{'=' * 60}

Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📈 Overall Statistics:
  Total Files Analyzed: {analysis.total_files}
  Files with Code Elements: {analysis.analyzed_files}
  Total Code Elements: {analysis.total_elements}
  Documented Elements: {analysis.documented_elements}
  Overall Coverage: {analysis.overall_coverage:.1f}%
  Overall Quality Score: {analysis.overall_quality:.1f}/1.0

📋 Coverage by Category:
"""
        
        for category, stats in analysis.summary_by_category.items():
            category_name = category.replace('_', ' ').title()
            report += f"""
  {category_name}:
    Files: {stats['files']}
    Elements: {stats['documented_elements']}/{stats['total_elements']}
    Coverage: {stats['coverage']:.1f}%
    Quality: {stats['quality']:.2f}/1.0
"""
        
        # Top issues
        if analysis.top_issues:
            report += f"""
⚠️  Top Issues ({len(analysis.top_issues)}):
"""
            for i, issue in enumerate(analysis.top_issues[:10], 1):
                report += f"  {i}. {issue}\n"
        
        # File-by-file breakdown for low coverage files
        low_coverage_files = [a for a in analysis.file_analyses if a.coverage_percentage < 80]
        if low_coverage_files:
            report += f"""
📉 Files Needing Attention (Coverage < 80%):
"""
            for analysis_file in sorted(low_coverage_files, key=lambda x: x.coverage_percentage):
                report += f"""
  {analysis_file.file_path}:
    Coverage: {analysis_file.coverage_percentage:.1f}%
    Quality: {analysis_file.quality_score:.2f}/1.0
    Elements: {analysis_file.documented_elements}/{analysis_file.total_elements}
    Issues: {len(analysis_file.issues)}
"""
        
        # Recommendations
        report += f"""
💡 Recommendations:

Priority 1 - Critical Files (API & Core):
  • Focus on files in /api/ and /core/ directories
  • Target: 100% coverage for public APIs
  • Ensure all functions have Args, Returns, and Examples

Priority 2 - Service Layer:
  • Complete service and model documentation
  • Target: 95% coverage
  • Focus on class-level documentation

Priority 3 - Supporting Files:
  • Document utility functions and test helpers
  • Target: 85% coverage
  • Add module-level documentation

Quality Improvements:
  • Add examples to complex functions
  • Include type information in docstrings
  • Ensure consistent formatting
  • Add performance notes where relevant

🎯 Next Steps:
  1. Run: python scripts/docstring_validator.py --priority-1
  2. Implement missing docstrings for critical files
  3. Run: python scripts/docstring_analyzer.py --compare-baseline
  4. Monitor progress with weekly reports
"""
        
        if output_file:
            output_file.write_text(report)
            print(f"Report saved to {output_file}")
        
        return report


def main():
    """Main entry point for the docstring analyzer."""
    parser = argparse.ArgumentParser(description="Analyze docstring coverage and quality")
    parser.add_argument("--project-root", type=Path, default=Path.cwd().parent,
                       help="Project root directory")
    parser.add_argument("--output", type=Path, help="Output file for report")
    parser.add_argument("--json", action="store_true", help="Output JSON format")
    parser.add_argument("--enforce-minimum", type=float, default=0,
                       help="Enforce minimum coverage percentage")
    parser.add_argument("--check-new-files", action="store_true",
                       help="Only check files modified in git")
    
    args = parser.parse_args()
    
    analyzer = DocstringAnalyzer(args.project_root)
    analysis = analyzer.analyze_project()
    
    if args.json:
        output = json.dumps(asdict(analysis), indent=2, default=str)
        if args.output:
            args.output.write_text(output)
        else:
            print(output)
    else:
        report = analyzer.generate_report(analysis, args.output)
        if not args.output:
            print(report)
    
    # Enforce minimum coverage if specified
    if args.enforce_minimum > 0:
        if analysis.overall_coverage < args.enforce_minimum:
            print(f"\n❌ Coverage {analysis.overall_coverage:.1f}% below minimum {args.enforce_minimum}%")
            sys.exit(1)
        else:
            print(f"\n✅ Coverage {analysis.overall_coverage:.1f}% meets minimum {args.enforce_minimum}%")


if __name__ == "__main__":
    main()
