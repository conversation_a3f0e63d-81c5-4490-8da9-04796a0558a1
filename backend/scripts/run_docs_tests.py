#!/usr/bin/env python3
"""
Comprehensive documentation testing runner for Blast-Radius Security Tool.

This script orchestrates all documentation testing activities including:
- Documentation build validation
- Docstring coverage analysis
- Link checking and validation
- Content quality assessment
- Performance benchmarking
- Security scanning
- Integration testing
"""

import argparse
import json
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional

import pytest


class DocumentationTestRunner:
    """Orchestrates comprehensive documentation testing."""
    
    def __init__(self, project_root: Path, verbose: bool = False):
        self.project_root = project_root
        self.verbose = verbose
        self.backend_dir = project_root / "backend"
        self.docs_dir = project_root / "docs"
        self.results = {}
        
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all documentation tests and return results."""
        print("🚀 Starting comprehensive documentation testing...")
        
        test_suites = [
            ("Build Tests", self.run_build_tests),
            ("Coverage Tests", self.run_coverage_tests),
            ("Quality Tests", self.run_quality_tests),
            ("Integration Tests", self.run_integration_tests),
            ("Performance Tests", self.run_performance_tests),
            ("Security Tests", self.run_security_tests),
            ("Accessibility Tests", self.run_accessibility_tests),
        ]
        
        for suite_name, test_func in test_suites:
            print(f"\n📋 Running {suite_name}...")
            try:
                result = test_func()
                self.results[suite_name] = result
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"   {status}")
            except Exception as e:
                self.results[suite_name] = False
                print(f"   ❌ ERROR: {e}")
                if self.verbose:
                    import traceback
                    traceback.print_exc()
        
        return self.results
    
    def run_build_tests(self) -> bool:
        """Run documentation build tests."""
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_documentation.py::TestDocumentationBuild",
            "-v" if self.verbose else "-q",
            "--tb=short"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True)
        return result.returncode == 0
    
    def run_coverage_tests(self) -> bool:
        """Run docstring coverage tests."""
        cmd = [
            sys.executable, "-m", "pytest", 
            "tests/test_documentation.py::TestDocstringCoverage",
            "-v" if self.verbose else "-q",
            "--tb=short"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True)
        return result.returncode == 0
    
    def run_quality_tests(self) -> bool:
        """Run documentation quality tests."""
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_documentation.py::TestDocumentationContent",
            "-v" if self.verbose else "-q", 
            "--tb=short"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True)
        return result.returncode == 0
    
    def run_integration_tests(self) -> bool:
        """Run documentation integration tests."""
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_docs_integration.py::TestAPIDocumentationIntegration",
            "-v" if self.verbose else "-q",
            "--tb=short"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True)
        return result.returncode == 0
    
    def run_performance_tests(self) -> bool:
        """Run documentation performance tests."""
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_docs_integration.py::TestDocumentationPerformance",
            "-v" if self.verbose else "-q",
            "--tb=short"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True)
        return result.returncode == 0
    
    def run_security_tests(self) -> bool:
        """Run documentation security tests."""
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_docs_integration.py::TestDocumentationSecurity", 
            "-v" if self.verbose else "-q",
            "--tb=short"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True)
        return result.returncode == 0
    
    def run_accessibility_tests(self) -> bool:
        """Run documentation accessibility tests."""
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_documentation.py::TestDocumentationAccessibility",
            "-v" if self.verbose else "-q",
            "--tb=short"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_dir, capture_output=True)
        return result.returncode == 0
    
    def run_sphinx_tests(self) -> bool:
        """Run Sphinx-specific tests."""
        tests = [
            self.test_sphinx_build,
            self.test_sphinx_linkcheck,
            self.test_sphinx_coverage,
            self.test_sphinx_doctest,
        ]
        
        for test in tests:
            if not test():
                return False
        
        return True
    
    def test_sphinx_build(self) -> bool:
        """Test Sphinx HTML build."""
        cmd = [
            "sphinx-build",
            "-b", "html",
            "-W",  # Treat warnings as errors
            "-E",  # Don't use saved environment
            str(self.docs_dir),
            str(self.docs_dir / "_build" / "test_html")
        ]
        
        result = subprocess.run(cmd, capture_output=True, timeout=300)
        return result.returncode == 0
    
    def test_sphinx_linkcheck(self) -> bool:
        """Test Sphinx link checking."""
        cmd = [
            "sphinx-build",
            "-b", "linkcheck",
            str(self.docs_dir),
            str(self.docs_dir / "_build" / "test_linkcheck")
        ]
        
        result = subprocess.run(cmd, capture_output=True, timeout=300)
        # Link check may have warnings but shouldn't fail completely
        return result.returncode in [0, 1]
    
    def test_sphinx_coverage(self) -> bool:
        """Test Sphinx coverage reporting."""
        cmd = [
            "sphinx-build",
            "-b", "coverage",
            str(self.docs_dir),
            str(self.docs_dir / "_build" / "test_coverage")
        ]
        
        result = subprocess.run(cmd, capture_output=True, timeout=300)
        return result.returncode == 0
    
    def test_sphinx_doctest(self) -> bool:
        """Test Sphinx doctest execution."""
        cmd = [
            "sphinx-build", 
            "-b", "doctest",
            str(self.docs_dir),
            str(self.docs_dir / "_build" / "test_doctest")
        ]
        
        result = subprocess.run(cmd, capture_output=True, timeout=300)
        return result.returncode == 0
    
    def generate_report(self) -> str:
        """Generate a comprehensive test report."""
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result)
        failed_tests = total_tests - passed_tests
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = f"""
📊 Documentation Testing Report
{'=' * 50}

📈 Overall Results:
  • Total Test Suites: {total_tests}
  • Passed: {passed_tests}
  • Failed: {failed_tests}
  • Success Rate: {success_rate:.1f}%

📋 Detailed Results:
"""
        
        for suite_name, result in self.results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            report += f"  • {suite_name}: {status}\n"
        
        if failed_tests > 0:
            report += f"""
⚠️  Failed Test Suites:
"""
            for suite_name, result in self.results.items():
                if not result:
                    report += f"  • {suite_name}\n"
        
        report += f"""
💡 Recommendations:
"""
        
        if not self.results.get("Build Tests", True):
            report += "  • Fix documentation build errors\n"
        
        if not self.results.get("Coverage Tests", True):
            report += "  • Improve docstring coverage\n"
        
        if not self.results.get("Quality Tests", True):
            report += "  • Address content quality issues\n"
        
        if not self.results.get("Integration Tests", True):
            report += "  • Fix API documentation integration\n"
        
        if not self.results.get("Performance Tests", True):
            report += "  • Optimize documentation performance\n"
        
        if not self.results.get("Security Tests", True):
            report += "  • Address security concerns in documentation\n"
        
        if success_rate == 100:
            report += "  🎉 All tests passed! Documentation is in excellent condition.\n"
        
        return report
    
    def save_results(self, output_file: Path):
        """Save test results to a file."""
        results_data = {
            "timestamp": time.time(),
            "results": self.results,
            "summary": {
                "total": len(self.results),
                "passed": sum(1 for r in self.results.values() if r),
                "failed": sum(1 for r in self.results.values() if not r),
                "success_rate": sum(1 for r in self.results.values() if r) / len(self.results) * 100
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(results_data, f, indent=2)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Run comprehensive documentation tests"
    )
    parser.add_argument(
        "--project-root", 
        type=Path, 
        default=Path(__file__).parent.parent.parent,
        help="Project root directory"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    parser.add_argument(
        "--output", "-o",
        type=Path,
        help="Output file for test results (JSON format)"
    )
    parser.add_argument(
        "--fail-fast",
        action="store_true", 
        help="Stop on first test failure"
    )
    parser.add_argument(
        "--suite",
        choices=["build", "coverage", "quality", "integration", "performance", "security", "accessibility"],
        help="Run only specific test suite"
    )
    
    args = parser.parse_args()
    
    runner = DocumentationTestRunner(args.project_root, args.verbose)
    
    if args.suite:
        # Run specific test suite
        suite_methods = {
            "build": runner.run_build_tests,
            "coverage": runner.run_coverage_tests,
            "quality": runner.run_quality_tests,
            "integration": runner.run_integration_tests,
            "performance": runner.run_performance_tests,
            "security": runner.run_security_tests,
            "accessibility": runner.run_accessibility_tests,
        }
        
        method = suite_methods[args.suite]
        success = method()
        runner.results[args.suite] = success
    else:
        # Run all tests
        runner.run_all_tests()
    
    # Generate and display report
    report = runner.generate_report()
    print(report)
    
    # Save results if requested
    if args.output:
        runner.save_results(args.output)
        print(f"\n📄 Results saved to {args.output}")
    
    # Exit with appropriate code
    all_passed = all(runner.results.values())
    if not all_passed:
        print("\n❌ Some documentation tests failed!")
        sys.exit(1)
    else:
        print("\n✅ All documentation tests passed!")
        sys.exit(0)


if __name__ == "__main__":
    main()
