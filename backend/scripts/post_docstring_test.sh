#!/bin/bash
"""
Post-implementation test suite for docstring management.

This script validates that docstring implementation hasn't broken any
functionality and measures improvement in documentation quality.
"""

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
BASELINE_DIR="$BACKEND_DIR/.baseline"
RESULTS_DIR="$BACKEND_DIR/.post_implementation"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${BLUE}🔍 Post-Implementation Validation Suite${NC}"
echo -e "${BLUE}=======================================${NC}"
echo "Project Root: $PROJECT_ROOT"
echo "Backend Dir: $BACKEND_DIR"
echo "Timestamp: $TIMESTAMP"
echo ""

# Check if baseline exists
if [ ! -d "$BASELINE_DIR" ]; then
    echo -e "${RED}❌ Baseline directory not found: $BASELINE_DIR${NC}"
    echo "Please run pre_docstring_test.sh first to establish baseline"
    exit 1
fi

# Create results directory
mkdir -p "$RESULTS_DIR"

cd "$BACKEND_DIR"

echo -e "${YELLOW}📋 Step 1: Environment Validation${NC}"
echo "Validating environment consistency..."

# Check Python version consistency
current_python=$(python --version 2>&1)
echo "Current Python: $current_python"

# Check virtual environment
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "Virtual Environment: $VIRTUAL_ENV"
else
    echo -e "${YELLOW}Warning: No virtual environment detected${NC}"
fi

echo ""

echo -e "${YELLOW}🧪 Step 2: Regression Testing${NC}"
echo "Running comprehensive test suite to check for regressions..."

# Run tests with coverage
echo "Running pytest with coverage..."
pytest tests/ \
    --cov=app \
    --cov-report=json \
    --cov-report=html \
    --cov-report=term \
    --json-report \
    --json-report-file="$RESULTS_DIR/test_results_current.json" \
    -v \
    > "$RESULTS_DIR/pytest_output_current.txt" 2>&1

test_exit_code=$?

if [ $test_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ All tests passed${NC}"
else
    echo -e "  ${RED}❌ Some tests failed (exit code: $test_exit_code)${NC}"
    echo "  Check $RESULTS_DIR/pytest_output_current.txt for details"
fi

# Save coverage data
if [ -f "coverage.json" ]; then
    cp "coverage.json" "$RESULTS_DIR/coverage_current.json"
    echo "  📊 Coverage data saved"
fi

if [ -d "htmlcov" ]; then
    cp -r "htmlcov" "$RESULTS_DIR/htmlcov_current"
    echo "  📊 HTML coverage report saved"
fi

echo ""

echo -e "${YELLOW}🔍 Step 3: Type Checking Validation${NC}"
echo "Running mypy type checking..."

mypy app/ \
    --json-report "$RESULTS_DIR/mypy_current.json" \
    --html-report "$RESULTS_DIR/mypy_html_current" \
    --txt-report "$RESULTS_DIR/mypy_txt_current" \
    > "$RESULTS_DIR/mypy_output_current.txt" 2>&1

mypy_exit_code=$?

if [ $mypy_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ No type checking errors${NC}"
else
    echo -e "  ${YELLOW}⚠️ Type checking issues found (exit code: $mypy_exit_code)${NC}"
    echo "  Check $RESULTS_DIR/mypy_output_current.txt for details"
fi

echo ""

echo -e "${YELLOW}🔒 Step 4: Security Validation${NC}"
echo "Running bandit security scan..."

bandit -r app/ \
    -f json \
    -o "$RESULTS_DIR/bandit_current.json" \
    > "$RESULTS_DIR/bandit_output_current.txt" 2>&1

bandit_exit_code=$?

if [ $bandit_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ No security issues found${NC}"
else
    echo -e "  ${YELLOW}⚠️ Security issues found (exit code: $bandit_exit_code)${NC}"
    echo "  Check $RESULTS_DIR/bandit_output_current.txt for details"
fi

echo ""

echo -e "${YELLOW}⚡ Step 5: Performance Validation${NC}"
echo "Checking performance impact..."

# Run performance test
python "$BASELINE_DIR/performance_test.py" > "$RESULTS_DIR/performance_current.txt" 2>&1

# Compare performance
if [ -f "$BASELINE_DIR/performance_baseline.json" ] && [ -f "$BASELINE_DIR/performance_baseline.json" ]; then
    echo "  📊 Performance comparison available"
else
    echo -e "  ${YELLOW}⚠️ Performance baseline not found${NC}"
fi

echo ""

echo -e "${YELLOW}📝 Step 6: Docstring Quality Validation${NC}"
echo "Analyzing docstring improvements..."

# Run current docstring analysis
python scripts/docstring_analyzer.py \
    --project-root "$PROJECT_ROOT" \
    --output "$RESULTS_DIR/docstring_current_report.txt" \
    --json > "$RESULTS_DIR/docstring_current.json"

echo "  📊 Current docstring analysis complete"

# Run docstring validation
python scripts/docstring_validator.py \
    --all \
    > "$RESULTS_DIR/docstring_validation.txt" 2>&1

validation_exit_code=$?

if [ $validation_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ All docstrings passed validation${NC}"
else
    echo -e "  ${YELLOW}⚠️ Some docstring quality issues found${NC}"
    echo "  Check $RESULTS_DIR/docstring_validation.txt for details"
fi

echo ""

echo -e "${YELLOW}🏗️ Step 7: Documentation Build Validation${NC}"
echo "Testing documentation build..."

# Test Sphinx documentation build
cd "$PROJECT_ROOT/docs"
sphinx-build -b html . "_build/post_test" \
    > "$RESULTS_DIR/sphinx_build_current.txt" 2>&1

sphinx_exit_code=$?

if [ $sphinx_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ Documentation builds successfully${NC}"
else
    echo -e "  ${RED}❌ Documentation build failed (exit code: $sphinx_exit_code)${NC}"
    echo "  Check $RESULTS_DIR/sphinx_build_current.txt for details"
fi

cd "$BACKEND_DIR"

echo ""

echo -e "${YELLOW}📊 Step 8: Comparison Analysis${NC}"
echo "Comparing results with baseline..."

# Create comparison script
cat > "$RESULTS_DIR/compare_results.py" << 'EOF'
#!/usr/bin/env python3
"""Compare post-implementation results with baseline."""

import json
import sys
from pathlib import Path

def load_json_safe(file_path):
    """Load JSON file safely."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Warning: Could not load {file_path}: {e}")
        return None

def compare_coverage(baseline_file, current_file):
    """Compare test coverage."""
    baseline = load_json_safe(baseline_file)
    current = load_json_safe(current_file)
    
    if not baseline or not current:
        return "Coverage comparison unavailable"
    
    baseline_pct = baseline.get('totals', {}).get('percent_covered', 0)
    current_pct = current.get('totals', {}).get('percent_covered', 0)
    
    diff = current_pct - baseline_pct
    
    if diff > 0:
        return f"✅ Coverage improved: {baseline_pct:.1f}% → {current_pct:.1f}% (+{diff:.1f}%)"
    elif diff < 0:
        return f"⚠️ Coverage decreased: {baseline_pct:.1f}% → {current_pct:.1f}% ({diff:.1f}%)"
    else:
        return f"➡️ Coverage unchanged: {current_pct:.1f}%"

def compare_docstrings(baseline_file, current_file):
    """Compare docstring coverage."""
    baseline = load_json_safe(baseline_file)
    current = load_json_safe(current_file)
    
    if not baseline or not current:
        return "Docstring comparison unavailable"
    
    baseline_cov = baseline.get('overall_coverage', 0)
    current_cov = current.get('overall_coverage', 0)
    
    baseline_qual = baseline.get('overall_quality', 0)
    current_qual = current.get('overall_quality', 0)
    
    cov_diff = current_cov - baseline_cov
    qual_diff = current_qual - baseline_qual
    
    result = []
    
    if cov_diff > 0:
        result.append(f"✅ Docstring coverage improved: {baseline_cov:.1f}% → {current_cov:.1f}% (+{cov_diff:.1f}%)")
    elif cov_diff < 0:
        result.append(f"⚠️ Docstring coverage decreased: {baseline_cov:.1f}% → {current_cov:.1f}% ({cov_diff:.1f}%)")
    else:
        result.append(f"➡️ Docstring coverage unchanged: {current_cov:.1f}%")
    
    if qual_diff > 0:
        result.append(f"✅ Docstring quality improved: {baseline_qual:.2f} → {current_qual:.2f} (+{qual_diff:.2f})")
    elif qual_diff < 0:
        result.append(f"⚠️ Docstring quality decreased: {baseline_qual:.2f} → {current_qual:.2f} ({qual_diff:.2f})")
    else:
        result.append(f"➡️ Docstring quality unchanged: {current_qual:.2f}")
    
    return "\n".join(result)

def main():
    """Main comparison function."""
    baseline_dir = Path(".baseline")
    results_dir = Path(".post_implementation")
    
    print("📊 Comparison Analysis Results")
    print("=" * 40)
    print()
    
    # Compare test coverage
    print("🧪 Test Coverage:")
    coverage_result = compare_coverage(
        baseline_dir / "coverage_baseline.json",
        results_dir / "coverage_current.json"
    )
    print(f"  {coverage_result}")
    print()
    
    # Compare docstring coverage
    print("📝 Docstring Analysis:")
    docstring_result = compare_docstrings(
        baseline_dir / "docstring_baseline.json",
        results_dir / "docstring_current.json"
    )
    for line in docstring_result.split('\n'):
        print(f"  {line}")
    print()
    
    # Load baseline summary for exit codes
    baseline_summary = load_json_safe(baseline_dir / "baseline_summary.json")
    if baseline_summary:
        baseline_tests = baseline_summary.get('test_results', {})
        print("🔍 Regression Analysis:")
        
        # Compare exit codes (these should be passed as arguments in real implementation)
        # For now, we'll just show the structure
        print("  Test Suite: Comparison needed")
        print("  Type Checking: Comparison needed")
        print("  Security Scan: Comparison needed")
        print("  Documentation Build: Comparison needed")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
EOF

# Run comparison
python "$RESULTS_DIR/compare_results.py" > "$RESULTS_DIR/comparison_report.txt"

# Display comparison results
cat "$RESULTS_DIR/comparison_report.txt"

echo ""

echo -e "${YELLOW}📋 Step 9: Final Validation Summary${NC}"
echo "Generating comprehensive validation summary..."

# Create validation summary
cat > "$RESULTS_DIR/validation_summary.json" << EOF
{
  "timestamp": "$TIMESTAMP",
  "baseline_timestamp": "$(cat $BASELINE_DIR/baseline_summary.json | grep timestamp | cut -d'"' -f4)",
  "test_results": {
    "pytest_exit_code": $test_exit_code,
    "mypy_exit_code": $mypy_exit_code,
    "bandit_exit_code": $bandit_exit_code,
    "sphinx_exit_code": $sphinx_exit_code,
    "validation_exit_code": $validation_exit_code
  },
  "regression_status": "$([ $test_exit_code -eq 0 ] && echo "PASS" || echo "FAIL")",
  "files_created": [
    "test_results_current.json",
    "coverage_current.json",
    "mypy_current.json",
    "bandit_current.json",
    "docstring_current.json",
    "docstring_validation.txt",
    "sphinx_build_current.txt",
    "comparison_report.txt"
  ]
}
EOF

# Create human-readable summary
cat > "$RESULTS_DIR/VALIDATION_SUMMARY.md" << EOF
# Post-Implementation Validation Summary

**Generated:** $(date)
**Timestamp:** $TIMESTAMP

## Regression Test Results

| Test Type | Status | Exit Code | Regression Check |
|-----------|--------|-----------|------------------|
| PyTest | $([ $test_exit_code -eq 0 ] && echo "✅ PASS" || echo "❌ FAIL") | $test_exit_code | $([ $test_exit_code -eq 0 ] && echo "✅ No Regression" || echo "❌ Regression Detected") |
| MyPy | $([ $mypy_exit_code -eq 0 ] && echo "✅ PASS" || echo "⚠️ ISSUES") | $mypy_exit_code | Type checking status |
| Bandit | $([ $bandit_exit_code -eq 0 ] && echo "✅ PASS" || echo "⚠️ ISSUES") | $bandit_exit_code | Security scan status |
| Sphinx | $([ $sphinx_exit_code -eq 0 ] && echo "✅ PASS" || echo "❌ FAIL") | $sphinx_exit_code | Documentation build |
| Docstring Validation | $([ $validation_exit_code -eq 0 ] && echo "✅ PASS" || echo "⚠️ ISSUES") | $validation_exit_code | Quality validation |

## Improvement Analysis

$(cat "$RESULTS_DIR/comparison_report.txt")

## Validation Status

**Overall Status:** $([ $test_exit_code -eq 0 ] && [ $sphinx_exit_code -eq 0 ] && echo "✅ VALIDATION PASSED" || echo "❌ VALIDATION FAILED")

### Critical Issues
$([ $test_exit_code -ne 0 ] && echo "- ❌ Test suite failures detected" || echo "- ✅ No test suite regressions")
$([ $sphinx_exit_code -ne 0 ] && echo "- ❌ Documentation build failures" || echo "- ✅ Documentation builds successfully")

### Recommendations
$([ $test_exit_code -ne 0 ] && echo "- Review test failures and fix any regressions")
$([ $validation_exit_code -ne 0 ] && echo "- Address docstring quality issues")
$([ $sphinx_exit_code -ne 0 ] && echo "- Fix documentation build errors")

## Files Generated

All validation files are stored in: \`$RESULTS_DIR\`

## Next Steps

1. **Review Results**: Check all validation files for issues
2. **Address Regressions**: Fix any functionality that was broken
3. **Improve Quality**: Address any docstring quality issues
4. **Monitor Progress**: Continue using docstring tracker for ongoing monitoring
EOF

echo -e "  ${GREEN}✅ Validation summary generated${NC}"

echo ""

# Final status determination
overall_status=0
if [ $test_exit_code -ne 0 ] || [ $sphinx_exit_code -ne 0 ]; then
    overall_status=1
fi

if [ $overall_status -eq 0 ]; then
    echo -e "${GREEN}🎉 Post-Implementation Validation PASSED!${NC}"
    echo -e "${GREEN}=========================================${NC}"
    echo ""
    echo "✅ Summary:"
    echo "  • No functionality regressions detected"
    echo "  • Documentation builds successfully"
    echo "  • Docstring implementation completed"
    echo ""
    echo "📊 Improvements:"
    cat "$RESULTS_DIR/comparison_report.txt" | grep -E "✅|improved"
    echo ""
    echo "🎯 Next Steps:"
    echo "  • Monitor ongoing docstring quality"
    echo "  • Continue implementation for remaining files"
    echo "  • Set up automated quality checks in CI/CD"
else
    echo -e "${RED}❌ Post-Implementation Validation FAILED!${NC}"
    echo -e "${RED}==========================================${NC}"
    echo ""
    echo "⚠️ Issues Detected:"
    [ $test_exit_code -ne 0 ] && echo "  • Test suite failures (regression)"
    [ $sphinx_exit_code -ne 0 ] && echo "  • Documentation build failures"
    echo ""
    echo "🔧 Required Actions:"
    echo "  • Review and fix test failures"
    echo "  • Fix documentation build issues"
    echo "  • Re-run validation after fixes"
fi

echo ""
echo "📁 Validation files saved to: $RESULTS_DIR"
echo "📋 Review summary: $RESULTS_DIR/VALIDATION_SUMMARY.md"
echo ""

exit $overall_status
