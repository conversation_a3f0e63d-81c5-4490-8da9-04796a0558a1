#!/usr/bin/env python3
"""
Progress tracking and reporting for docstring implementation.

This tool tracks progress, generates reports, and provides dashboard
functionality for monitoring docstring implementation across the project.
"""

import json
import sqlite3
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, List, Optional, Tu<PERSON>
import argparse

from docstring_analyzer import DocstringAnalyzer, ProjectAnalysis


@dataclass
class ProgressSnapshot:
    """Snapshot of progress at a specific point in time."""
    timestamp: datetime
    overall_coverage: float
    overall_quality: float
    total_files: int
    total_elements: int
    documented_elements: int
    category_breakdown: Dict[str, Dict[str, float]]
    top_issues: List[str]


@dataclass
class ProgressReport:
    """Comprehensive progress report."""
    current_snapshot: ProgressSnapshot
    previous_snapshot: Optional[ProgressSnapshot]
    coverage_trend: float
    quality_trend: float
    files_improved: int
    new_issues: List[str]
    resolved_issues: List[str]
    recommendations: List[str]


class DocstringTracker:
    """Tracks progress and generates reports for docstring implementation."""
    
    def __init__(self, project_root: Path):
        """Initialize the progress tracker.
        
        Args:
            project_root: Root directory of the project.
        """
        self.project_root = project_root
        self.db_path = project_root / "backend" / ".docstring_progress.db"
        self.analyzer = DocstringAnalyzer(project_root)
        self._init_database()
    
    def _init_database(self):
        """Initialize the progress tracking database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS progress_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    overall_coverage REAL NOT NULL,
                    overall_quality REAL NOT NULL,
                    total_files INTEGER NOT NULL,
                    total_elements INTEGER NOT NULL,
                    documented_elements INTEGER NOT NULL,
                    category_breakdown TEXT NOT NULL,
                    top_issues TEXT NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS file_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    snapshot_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    coverage REAL NOT NULL,
                    quality REAL NOT NULL,
                    total_elements INTEGER NOT NULL,
                    documented_elements INTEGER NOT NULL,
                    issues TEXT NOT NULL,
                    FOREIGN KEY (snapshot_id) REFERENCES progress_snapshots (id)
                )
            """)
    
    def capture_snapshot(self) -> ProgressSnapshot:
        """Capture current progress snapshot.
        
        Returns:
            ProgressSnapshot with current state.
        """
        print("📸 Capturing progress snapshot...")
        analysis = self.analyzer.analyze_project()
        
        snapshot = ProgressSnapshot(
            timestamp=datetime.now(),
            overall_coverage=analysis.overall_coverage,
            overall_quality=analysis.overall_quality,
            total_files=analysis.total_files,
            total_elements=analysis.total_elements,
            documented_elements=analysis.documented_elements,
            category_breakdown=analysis.summary_by_category,
            top_issues=analysis.top_issues[:10]  # Top 10 issues
        )
        
        # Save to database
        self._save_snapshot(snapshot, analysis)
        
        return snapshot
    
    def _save_snapshot(self, snapshot: ProgressSnapshot, analysis: ProjectAnalysis):
        """Save snapshot to database.
        
        Args:
            snapshot: Progress snapshot to save.
            analysis: Full project analysis.
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Insert snapshot
            cursor.execute("""
                INSERT INTO progress_snapshots 
                (timestamp, overall_coverage, overall_quality, total_files, 
                 total_elements, documented_elements, category_breakdown, top_issues)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                snapshot.timestamp.isoformat(),
                snapshot.overall_coverage,
                snapshot.overall_quality,
                snapshot.total_files,
                snapshot.total_elements,
                snapshot.documented_elements,
                json.dumps(snapshot.category_breakdown),
                json.dumps(snapshot.top_issues)
            ))
            
            snapshot_id = cursor.lastrowid
            
            # Insert file progress
            for file_analysis in analysis.file_analyses:
                cursor.execute("""
                    INSERT INTO file_progress
                    (snapshot_id, file_path, coverage, quality, total_elements,
                     documented_elements, issues)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    snapshot_id,
                    file_analysis.file_path,
                    file_analysis.coverage_percentage,
                    file_analysis.quality_score,
                    file_analysis.total_elements,
                    file_analysis.documented_elements,
                    json.dumps(file_analysis.issues)
                ))
    
    def get_latest_snapshot(self) -> Optional[ProgressSnapshot]:
        """Get the most recent progress snapshot.
        
        Returns:
            Latest ProgressSnapshot or None if no snapshots exist.
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, overall_coverage, overall_quality, total_files,
                       total_elements, documented_elements, category_breakdown, top_issues
                FROM progress_snapshots
                ORDER BY timestamp DESC
                LIMIT 1
            """)
            
            row = cursor.fetchone()
            if not row:
                return None
            
            return ProgressSnapshot(
                timestamp=datetime.fromisoformat(row[0]),
                overall_coverage=row[1],
                overall_quality=row[2],
                total_files=row[3],
                total_elements=row[4],
                documented_elements=row[5],
                category_breakdown=json.loads(row[6]),
                top_issues=json.loads(row[7])
            )
    
    def get_previous_snapshot(self, before: datetime) -> Optional[ProgressSnapshot]:
        """Get the snapshot before a given timestamp.
        
        Args:
            before: Timestamp to search before.
            
        Returns:
            Previous ProgressSnapshot or None if not found.
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, overall_coverage, overall_quality, total_files,
                       total_elements, documented_elements, category_breakdown, top_issues
                FROM progress_snapshots
                WHERE timestamp < ?
                ORDER BY timestamp DESC
                LIMIT 1
            """, (before.isoformat(),))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            return ProgressSnapshot(
                timestamp=datetime.fromisoformat(row[0]),
                overall_coverage=row[1],
                overall_quality=row[2],
                total_files=row[3],
                total_elements=row[4],
                documented_elements=row[5],
                category_breakdown=json.loads(row[6]),
                top_issues=json.loads(row[7])
            )
    
    def generate_progress_report(self) -> ProgressReport:
        """Generate comprehensive progress report.
        
        Returns:
            ProgressReport with current progress and trends.
        """
        current = self.capture_snapshot()
        previous = self.get_previous_snapshot(current.timestamp)
        
        # Calculate trends
        coverage_trend = 0.0
        quality_trend = 0.0
        files_improved = 0
        
        if previous:
            coverage_trend = current.overall_coverage - previous.overall_coverage
            quality_trend = current.overall_quality - previous.overall_quality
            files_improved = self._count_improved_files(previous, current)
        
        # Identify new and resolved issues
        new_issues = []
        resolved_issues = []
        
        if previous:
            previous_issues_set = set(previous.top_issues)
            current_issues_set = set(current.top_issues)
            
            new_issues = list(current_issues_set - previous_issues_set)
            resolved_issues = list(previous_issues_set - current_issues_set)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(current, previous)
        
        return ProgressReport(
            current_snapshot=current,
            previous_snapshot=previous,
            coverage_trend=coverage_trend,
            quality_trend=quality_trend,
            files_improved=files_improved,
            new_issues=new_issues,
            resolved_issues=resolved_issues,
            recommendations=recommendations
        )
    
    def _count_improved_files(self, previous: ProgressSnapshot, current: ProgressSnapshot) -> int:
        """Count files that have improved since previous snapshot.
        
        Args:
            previous: Previous snapshot.
            current: Current snapshot.
            
        Returns:
            Number of improved files.
        """
        # This would require comparing file-level data from database
        # For now, return estimated improvement based on overall trends
        if current.overall_coverage > previous.overall_coverage:
            return max(1, int((current.overall_coverage - previous.overall_coverage) * 10))
        return 0
    
    def _generate_recommendations(self, current: ProgressSnapshot, 
                                previous: Optional[ProgressSnapshot]) -> List[str]:
        """Generate recommendations based on current progress.
        
        Args:
            current: Current snapshot.
            previous: Previous snapshot (optional).
            
        Returns:
            List of recommendation strings.
        """
        recommendations = []
        
        # Coverage-based recommendations
        if current.overall_coverage < 50:
            recommendations.append("🚨 Critical: Overall coverage is below 50%. Focus on Priority 1 files (API & Core)")
        elif current.overall_coverage < 80:
            recommendations.append("⚠️ Focus on completing Priority 1 files to reach 80% coverage milestone")
        elif current.overall_coverage < 95:
            recommendations.append("📈 Good progress! Continue with Priority 2 files (Services & Models)")
        else:
            recommendations.append("🎉 Excellent coverage! Focus on quality improvements and examples")
        
        # Quality-based recommendations
        if current.overall_quality < 0.7:
            recommendations.append("📝 Quality needs improvement. Focus on adding Args, Returns, and Examples sections")
        elif current.overall_quality < 0.9:
            recommendations.append("✨ Good quality! Add more examples and improve parameter documentation")
        
        # Category-specific recommendations
        for category, stats in current.category_breakdown.items():
            if stats['coverage'] < 70:
                category_name = category.replace('_', ' ').title()
                recommendations.append(f"🎯 {category_name} coverage is low ({stats['coverage']:.1f}%) - prioritise this area")
        
        # Trend-based recommendations
        if previous:
            if current.overall_coverage <= previous.overall_coverage:
                recommendations.append("📉 Coverage has stagnated. Review implementation strategy and remove blockers")
            
            if current.overall_quality < previous.overall_quality:
                recommendations.append("⚠️ Quality has decreased. Review recent changes and ensure standards compliance")
        
        # Issue-based recommendations
        if len(current.top_issues) > 20:
            recommendations.append("🔧 High number of issues detected. Run automated fixes where possible")
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def generate_dashboard(self) -> str:
        """Generate progress dashboard for monitoring.
        
        Returns:
            Dashboard content as formatted string.
        """
        report = self.generate_progress_report()
        current = report.current_snapshot
        
        # Calculate time since last snapshot
        time_since_last = "N/A"
        if report.previous_snapshot:
            delta = current.timestamp - report.previous_snapshot.timestamp
            if delta.days > 0:
                time_since_last = f"{delta.days} days ago"
            else:
                hours = delta.seconds // 3600
                time_since_last = f"{hours} hours ago"
        
        dashboard = f"""
🚀 Docstring Implementation Dashboard
{'=' * 50}

📊 Current Status (as of {current.timestamp.strftime('%Y-%m-%d %H:%M')})
  Overall Coverage: {current.overall_coverage:.1f}%
  Overall Quality:  {current.overall_quality:.2f}/1.0
  Total Elements:   {current.documented_elements:,}/{current.total_elements:,}
  Files Analyzed:   {current.total_files}

📈 Progress Trends (since {time_since_last})
  Coverage Change:  {report.coverage_trend:+.1f}%
  Quality Change:   {report.quality_trend:+.2f}
  Files Improved:   {report.files_improved}

📋 Coverage by Category:
"""
        
        for category, stats in current.category_breakdown.items():
            category_name = category.replace('_', ' ').title()
            coverage_bar = self._generate_progress_bar(stats['coverage'])
            dashboard += f"  {category_name:<15} {coverage_bar} {stats['coverage']:5.1f}% ({stats['documented_elements']}/{stats['total_elements']})\n"
        
        dashboard += f"""
🎯 Top Recommendations:
"""
        for i, rec in enumerate(report.recommendations, 1):
            dashboard += f"  {i}. {rec}\n"
        
        if report.new_issues:
            dashboard += f"""
🆕 New Issues ({len(report.new_issues)}):
"""
            for issue in report.new_issues[:5]:
                dashboard += f"  • {issue}\n"
        
        if report.resolved_issues:
            dashboard += f"""
✅ Resolved Issues ({len(report.resolved_issues)}):
"""
            for issue in report.resolved_issues[:5]:
                dashboard += f"  • {issue}\n"
        
        # Next milestones
        dashboard += f"""
🎯 Next Milestones:
"""
        if current.overall_coverage < 80:
            dashboard += f"  • Reach 80% overall coverage ({80 - current.overall_coverage:.1f}% to go)\n"
        elif current.overall_coverage < 95:
            dashboard += f"  • Reach 95% overall coverage ({95 - current.overall_coverage:.1f}% to go)\n"
        else:
            dashboard += f"  • Maintain 95%+ coverage and improve quality\n"
        
        if current.overall_quality < 0.9:
            dashboard += f"  • Reach 0.9 quality score ({0.9 - current.overall_quality:.2f} to go)\n"
        
        dashboard += f"""
📅 Quick Actions:
  • Run analysis:     python scripts/docstring_analyzer.py --report
  • Validate quality: python scripts/docstring_validator.py --priority-1
  • Update progress:  python scripts/docstring_tracker.py --capture
  • View trends:      python scripts/docstring_tracker.py --trends
"""
        
        return dashboard
    
    def _generate_progress_bar(self, percentage: float, width: int = 20) -> str:
        """Generate a text-based progress bar.
        
        Args:
            percentage: Percentage complete (0-100).
            width: Width of the progress bar in characters.
            
        Returns:
            Progress bar string.
        """
        filled = int(percentage / 100 * width)
        bar = '█' * filled + '░' * (width - filled)
        return f"[{bar}]"
    
    def get_trend_data(self, days: int = 30) -> List[ProgressSnapshot]:
        """Get trend data for the specified number of days.
        
        Args:
            days: Number of days to look back.
            
        Returns:
            List of progress snapshots within the timeframe.
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT timestamp, overall_coverage, overall_quality, total_files,
                       total_elements, documented_elements, category_breakdown, top_issues
                FROM progress_snapshots
                WHERE timestamp >= ?
                ORDER BY timestamp ASC
            """, (cutoff_date.isoformat(),))
            
            snapshots = []
            for row in cursor.fetchall():
                snapshots.append(ProgressSnapshot(
                    timestamp=datetime.fromisoformat(row[0]),
                    overall_coverage=row[1],
                    overall_quality=row[2],
                    total_files=row[3],
                    total_elements=row[4],
                    documented_elements=row[5],
                    category_breakdown=json.loads(row[6]),
                    top_issues=json.loads(row[7])
                ))
            
            return snapshots


def main():
    """Main entry point for the docstring tracker."""
    parser = argparse.ArgumentParser(description="Track docstring implementation progress")
    parser.add_argument("--capture", action="store_true", help="Capture current progress snapshot")
    parser.add_argument("--dashboard", action="store_true", help="Generate progress dashboard")
    parser.add_argument("--report", action="store_true", help="Generate detailed progress report")
    parser.add_argument("--trends", action="store_true", help="Show trend data")
    parser.add_argument("--days", type=int, default=30, help="Days to look back for trends")
    parser.add_argument("--output", type=Path, help="Output file for report")
    
    args = parser.parse_args()
    
    project_root = Path.cwd().parent if Path.cwd().name == "backend" else Path.cwd()
    tracker = DocstringTracker(project_root)
    
    if args.capture:
        snapshot = tracker.capture_snapshot()
        print(f"✅ Snapshot captured: {snapshot.overall_coverage:.1f}% coverage, {snapshot.overall_quality:.2f} quality")
    
    elif args.dashboard:
        dashboard = tracker.generate_dashboard()
        if args.output:
            args.output.write_text(dashboard)
            print(f"Dashboard saved to {args.output}")
        else:
            print(dashboard)
    
    elif args.report:
        report = tracker.generate_progress_report()
        # Generate detailed report (implementation would be similar to dashboard but more detailed)
        print("📊 Detailed progress report generated")
    
    elif args.trends:
        snapshots = tracker.get_trend_data(args.days)
        if snapshots:
            print(f"📈 Trend data for last {args.days} days:")
            for snapshot in snapshots:
                print(f"  {snapshot.timestamp.strftime('%Y-%m-%d')}: {snapshot.overall_coverage:.1f}% coverage")
        else:
            print("No trend data available")
    
    else:
        # Default: show dashboard
        dashboard = tracker.generate_dashboard()
        print(dashboard)


if __name__ == "__main__":
    main()
