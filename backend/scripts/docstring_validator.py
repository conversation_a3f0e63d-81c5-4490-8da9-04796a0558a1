#!/usr/bin/env python3
"""
Docstring quality validation and enforcement tool.

This tool validates docstring quality against established standards,
providing detailed feedback and automated quality assurance.
"""

import ast
import re
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
import argparse


@dataclass
class ValidationIssue:
    """Represents a docstring validation issue."""
    severity: str  # 'error', 'warning', 'info'
    code: str
    message: str
    line_number: int
    suggestion: Optional[str] = None


@dataclass
class ValidationResult:
    """Results of docstring validation."""
    file_path: str
    element_name: str
    element_type: str
    passed: bool
    score: float
    issues: List[ValidationIssue]


class DocstringValidator:
    """Validates docstring quality against established standards."""
    
    def __init__(self):
        """Initialize the docstring validator."""
        self.quality_rules = {
            'DS001': 'Missing docstring',
            'DS002': 'Docstring too short (minimum 10 words)',
            'DS003': 'Missing Args section for function with parameters',
            'DS004': 'Missing Returns section for function with return value',
            'DS005': 'Missing Raises section for function that raises exceptions',
            'DS006': 'Missing Examples section for complex function',
            'DS007': 'Inconsistent docstring format',
            'DS008': 'Missing type information in parameters',
            'DS009': 'Unclear or vague description',
            'DS010': 'Missing module-level docstring',
            'DS011': 'Docstring does not end with period',
            'DS012': 'Parameter not documented in Args section',
            'DS013': 'Documented parameter not in function signature',
            'DS014': 'Missing class attribute documentation',
            'DS015': 'Example code has syntax errors'
        }
        
        # Words that indicate vague descriptions
        self.vague_words = {
            'this', 'that', 'it', 'stuff', 'thing', 'something', 'anything',
            'everything', 'nothing', 'some', 'any', 'all', 'various'
        }
        
        # Common exception patterns
        self.exception_patterns = [
            r'raise\s+\w+Error',
            r'raise\s+\w+Exception',
            r'raise\s+ValueError',
            r'raise\s+TypeError',
            r'raise\s+RuntimeError',
            r'raise\s+HTTPException'
        ]
    
    def validate_docstring(self, docstring: str, context: Dict) -> ValidationResult:
        """Validate individual docstring quality.
        
        Args:
            docstring: The docstring content to validate.
            context: Context information about the code element.
            
        Returns:
            ValidationResult with validation outcome and issues.
        """
        issues = []
        
        if not docstring:
            issues.append(ValidationIssue(
                severity='error',
                code='DS001',
                message=self.quality_rules['DS001'],
                line_number=context.get('line_number', 0),
                suggestion='Add a descriptive docstring explaining the purpose and usage'
            ))
            return ValidationResult(
                file_path=context.get('file_path', ''),
                element_name=context.get('name', ''),
                element_type=context.get('type', ''),
                passed=False,
                score=0.0,
                issues=issues
            )
        
        # Check docstring length
        word_count = len(docstring.split())
        if word_count < 10:
            issues.append(ValidationIssue(
                severity='warning',
                code='DS002',
                message=f"{self.quality_rules['DS002']} (current: {word_count} words)",
                line_number=context.get('line_number', 0),
                suggestion='Expand the description to provide more detail about purpose and usage'
            ))
        
        # Check for proper ending punctuation
        if not docstring.strip().endswith('.'):
            issues.append(ValidationIssue(
                severity='info',
                code='DS011',
                message=self.quality_rules['DS011'],
                line_number=context.get('line_number', 0),
                suggestion='End the docstring with a period for consistency'
            ))
        
        # Function-specific validations
        if context.get('type') in ['function', 'method']:
            issues.extend(self._validate_function_docstring(docstring, context))
        
        # Class-specific validations
        elif context.get('type') == 'class':
            issues.extend(self._validate_class_docstring(docstring, context))
        
        # Module-specific validations
        elif context.get('type') == 'module':
            issues.extend(self._validate_module_docstring(docstring, context))
        
        # Check for vague descriptions
        issues.extend(self._check_vague_description(docstring, context))
        
        # Check for example syntax
        issues.extend(self._validate_examples(docstring, context))
        
        # Calculate overall score
        score = self._calculate_validation_score(issues)
        passed = score >= 0.8 and not any(issue.severity == 'error' for issue in issues)
        
        return ValidationResult(
            file_path=context.get('file_path', ''),
            element_name=context.get('name', ''),
            element_type=context.get('type', ''),
            passed=passed,
            score=score,
            issues=issues
        )
    
    def _validate_function_docstring(self, docstring: str, context: Dict) -> List[ValidationIssue]:
        """Validate function-specific docstring requirements.
        
        Args:
            docstring: The docstring content.
            context: Function context information.
            
        Returns:
            List of validation issues.
        """
        issues = []
        parameters = context.get('parameters', [])
        has_return = context.get('has_return', False)
        function_body = context.get('function_body', '')
        
        # Check for Args section if function has parameters
        if parameters and len(parameters) > 1:  # Exclude 'self' parameter
            if not re.search(r'\b(Args?|Arguments?|Parameters?):', docstring, re.IGNORECASE):
                issues.append(ValidationIssue(
                    severity='error',
                    code='DS003',
                    message=self.quality_rules['DS003'],
                    line_number=context.get('line_number', 0),
                    suggestion='Add an Args section documenting each parameter'
                ))
            else:
                # Check that all parameters are documented
                issues.extend(self._check_parameter_documentation(docstring, parameters, context))
        
        # Check for Returns section if function returns something
        if has_return or 'return ' in function_body:
            if not re.search(r'\b(Returns?|Return):', docstring, re.IGNORECASE):
                issues.append(ValidationIssue(
                    severity='error',
                    code='DS004',
                    message=self.quality_rules['DS004'],
                    line_number=context.get('line_number', 0),
                    suggestion='Add a Returns section describing the return value'
                ))
        
        # Check for Raises section if function raises exceptions
        if any(re.search(pattern, function_body) for pattern in self.exception_patterns):
            if not re.search(r'\b(Raises?|Raise):', docstring, re.IGNORECASE):
                issues.append(ValidationIssue(
                    severity='warning',
                    code='DS005',
                    message=self.quality_rules['DS005'],
                    line_number=context.get('line_number', 0),
                    suggestion='Add a Raises section documenting possible exceptions'
                ))
        
        # Check for Examples section for complex functions
        if len(parameters) > 3 or len(function_body.split('\n')) > 20:
            if not re.search(r'\b(Examples?|Example):', docstring, re.IGNORECASE):
                issues.append(ValidationIssue(
                    severity='info',
                    code='DS006',
                    message=self.quality_rules['DS006'],
                    line_number=context.get('line_number', 0),
                    suggestion='Consider adding an Examples section for complex functions'
                ))
        
        return issues
    
    def _validate_class_docstring(self, docstring: str, context: Dict) -> List[ValidationIssue]:
        """Validate class-specific docstring requirements.
        
        Args:
            docstring: The docstring content.
            context: Class context information.
            
        Returns:
            List of validation issues.
        """
        issues = []
        
        # Check for Attributes section if class has attributes
        class_body = context.get('class_body', '')
        if 'self.' in class_body:
            if not re.search(r'\b(Attributes?):', docstring, re.IGNORECASE):
                issues.append(ValidationIssue(
                    severity='warning',
                    code='DS014',
                    message=self.quality_rules['DS014'],
                    line_number=context.get('line_number', 0),
                    suggestion='Consider adding an Attributes section for class attributes'
                ))
        
        return issues
    
    def _validate_module_docstring(self, docstring: str, context: Dict) -> List[ValidationIssue]:
        """Validate module-specific docstring requirements.
        
        Args:
            docstring: The docstring content.
            context: Module context information.
            
        Returns:
            List of validation issues.
        """
        issues = []
        
        # Module docstrings should be more comprehensive
        word_count = len(docstring.split())
        if word_count < 20:
            issues.append(ValidationIssue(
                severity='warning',
                code='DS002',
                message=f"Module docstring should be more comprehensive (current: {word_count} words)",
                line_number=context.get('line_number', 0),
                suggestion='Expand to describe module purpose, main classes/functions, and usage examples'
            ))
        
        return issues
    
    def _check_parameter_documentation(self, docstring: str, parameters: List[str], 
                                     context: Dict) -> List[ValidationIssue]:
        """Check that all parameters are properly documented.
        
        Args:
            docstring: The docstring content.
            parameters: List of parameter names.
            context: Function context information.
            
        Returns:
            List of validation issues.
        """
        issues = []
        
        # Extract documented parameters from Args section
        args_match = re.search(r'(Args?|Arguments?|Parameters?):(.*?)(?=\n\s*\n|\n\s*[A-Z][a-z]+:|\Z)', 
                              docstring, re.IGNORECASE | re.DOTALL)
        
        if args_match:
            args_section = args_match.group(2)
            documented_params = re.findall(r'^\s*(\w+)(?:\s*\([^)]+\))?\s*:', args_section, re.MULTILINE)
            documented_params = [param.strip() for param in documented_params]
            
            # Check for undocumented parameters (excluding 'self' and 'cls')
            function_params = [p for p in parameters if p not in ['self', 'cls']]
            for param in function_params:
                if param not in documented_params:
                    issues.append(ValidationIssue(
                        severity='error',
                        code='DS012',
                        message=f"{self.quality_rules['DS012']}: '{param}'",
                        line_number=context.get('line_number', 0),
                        suggestion=f"Add documentation for parameter '{param}' in the Args section"
                    ))
            
            # Check for documented parameters not in signature
            for param in documented_params:
                if param not in parameters:
                    issues.append(ValidationIssue(
                        severity='warning',
                        code='DS013',
                        message=f"{self.quality_rules['DS013']}: '{param}'",
                        line_number=context.get('line_number', 0),
                        suggestion=f"Remove documentation for non-existent parameter '{param}'"
                    ))
        
        return issues
    
    def _check_vague_description(self, docstring: str, context: Dict) -> List[ValidationIssue]:
        """Check for vague or unclear descriptions.
        
        Args:
            docstring: The docstring content.
            context: Element context information.
            
        Returns:
            List of validation issues.
        """
        issues = []
        
        # Get the first sentence (description)
        first_sentence = docstring.split('.')[0].lower()
        
        # Check for vague words
        vague_found = [word for word in self.vague_words if word in first_sentence.split()]
        if vague_found:
            issues.append(ValidationIssue(
                severity='info',
                code='DS009',
                message=f"{self.quality_rules['DS009']} (vague words: {', '.join(vague_found)})",
                line_number=context.get('line_number', 0),
                suggestion='Use more specific and descriptive language'
            ))
        
        return issues
    
    def _validate_examples(self, docstring: str, context: Dict) -> List[ValidationIssue]:
        """Validate example code in docstrings.
        
        Args:
            docstring: The docstring content.
            context: Element context information.
            
        Returns:
            List of validation issues.
        """
        issues = []
        
        # Find example sections
        example_matches = re.finditer(r'(Examples?|Example):(.*?)(?=\n\s*\n|\n\s*[A-Z][a-z]+:|\Z)', 
                                    docstring, re.IGNORECASE | re.DOTALL)
        
        for match in example_matches:
            example_content = match.group(2)
            
            # Extract code blocks (lines starting with >>> or indented)
            code_lines = []
            for line in example_content.split('\n'):
                if line.strip().startswith('>>>') or (line.startswith('    ') and line.strip()):
                    code_lines.append(line.strip().lstrip('>>> '))
            
            # Basic syntax check for Python code
            if code_lines:
                code_block = '\n'.join(code_lines)
                try:
                    ast.parse(code_block)
                except SyntaxError:
                    issues.append(ValidationIssue(
                        severity='warning',
                        code='DS015',
                        message=self.quality_rules['DS015'],
                        line_number=context.get('line_number', 0),
                        suggestion='Fix syntax errors in example code'
                    ))
        
        return issues
    
    def _calculate_validation_score(self, issues: List[ValidationIssue]) -> float:
        """Calculate validation score based on issues.
        
        Args:
            issues: List of validation issues.
            
        Returns:
            Score between 0.0 and 1.0.
        """
        if not issues:
            return 1.0
        
        # Weight issues by severity
        severity_weights = {'error': 0.3, 'warning': 0.1, 'info': 0.05}
        total_penalty = sum(severity_weights.get(issue.severity, 0.1) for issue in issues)
        
        # Cap the penalty at 1.0
        total_penalty = min(total_penalty, 1.0)
        
        return max(0.0, 1.0 - total_penalty)
    
    def validate_file(self, file_path: Path) -> List[ValidationResult]:
        """Validate all docstrings in a Python file.
        
        Args:
            file_path: Path to the Python file to validate.
            
        Returns:
            List of validation results for each code element.
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            results = []
            
            # Validate module docstring
            module_context = {
                'file_path': str(file_path),
                'name': file_path.stem,
                'type': 'module',
                'line_number': 1
            }
            
            if (tree.body and isinstance(tree.body[0], ast.Expr) and 
                isinstance(tree.body[0].value, ast.Constant) and 
                isinstance(tree.body[0].value.value, str)):
                module_docstring = tree.body[0].value.value
                module_context['line_number'] = tree.body[0].lineno
            else:
                module_docstring = ""
            
            module_result = self.validate_docstring(module_docstring, module_context)
            results.append(module_result)
            
            # Validate all functions and classes
            for node in ast.walk(tree):
                if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef, ast.ClassDef)):
                    context = self._build_context(node, file_path, content)
                    
                    # Get docstring
                    docstring = ""
                    if (node.body and isinstance(node.body[0], ast.Expr) and 
                        isinstance(node.body[0].value, ast.Constant) and 
                        isinstance(node.body[0].value.value, str)):
                        docstring = node.body[0].value.value
                    
                    result = self.validate_docstring(docstring, context)
                    results.append(result)
            
            return results
            
        except Exception as e:
            # Return error result for file
            error_result = ValidationResult(
                file_path=str(file_path),
                element_name=file_path.stem,
                element_type='file',
                passed=False,
                score=0.0,
                issues=[ValidationIssue(
                    severity='error',
                    code='FILE_ERROR',
                    message=f"Error processing file: {str(e)}",
                    line_number=0
                )]
            )
            return [error_result]
    
    def _build_context(self, node: ast.AST, file_path: Path, content: str) -> Dict:
        """Build context information for a code element.
        
        Args:
            node: AST node to build context for.
            file_path: Path to the file containing the node.
            content: Full file content.
            
        Returns:
            Context dictionary with element information.
        """
        context = {
            'file_path': str(file_path),
            'name': node.name,
            'line_number': node.lineno,
            'parameters': [],
            'has_return': False,
            'function_body': '',
            'class_body': ''
        }
        
        if isinstance(node, ast.ClassDef):
            context['type'] = 'class'
            # Get class body for attribute analysis
            class_lines = content.split('\n')[node.lineno-1:node.end_lineno if hasattr(node, 'end_lineno') else len(content.split('\n'))]
            context['class_body'] = '\n'.join(class_lines)
            
        elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
            context['type'] = 'method' if self._is_method(node) else 'function'
            context['parameters'] = [arg.arg for arg in node.args.args]
            context['has_return'] = node.returns is not None
            
            # Get function body for analysis
            func_lines = content.split('\n')[node.lineno-1:node.end_lineno if hasattr(node, 'end_lineno') else len(content.split('\n'))]
            context['function_body'] = '\n'.join(func_lines)
        
        return context
    
    def _is_method(self, node: ast.FunctionDef) -> bool:
        """Check if a function node is a method.
        
        Args:
            node: Function AST node to check.
            
        Returns:
            True if the function is a method, False otherwise.
        """
        return len(node.args.args) > 0 and node.args.args[0].arg in ('self', 'cls')


def main():
    """Main entry point for the docstring validator."""
    parser = argparse.ArgumentParser(description="Validate docstring quality")
    parser.add_argument("files", nargs="*", help="Python files to validate")
    parser.add_argument("--all", action="store_true", help="Validate all Python files in project")
    parser.add_argument("--strict", action="store_true", help="Strict mode - fail on any issues")
    parser.add_argument("--priority-1", action="store_true", help="Only validate priority 1 files")
    parser.add_argument("--output", type=Path, help="Output file for results")
    
    args = parser.parse_args()
    
    validator = DocstringValidator()
    all_results = []
    
    if args.all or args.priority_1:
        # Find Python files to validate
        project_root = Path.cwd().parent if Path.cwd().name == "backend" else Path.cwd()
        backend_dir = project_root / "backend"
        
        if args.priority_1:
            # Priority 1: API and core files
            file_patterns = [
                backend_dir / "app" / "api" / "**" / "*.py",
                backend_dir / "app" / "core" / "*.py",
                backend_dir / "app" / "main.py"
            ]
        else:
            # All Python files
            file_patterns = [
                backend_dir / "app" / "**" / "*.py",
                backend_dir / "tests" / "**" / "*.py",
                backend_dir / "scripts" / "*.py"
            ]
        
        files_to_validate = []
        for pattern in file_patterns:
            files_to_validate.extend(pattern.parent.rglob(pattern.name))
        
        # Remove duplicates and filter
        files_to_validate = list(set(f for f in files_to_validate if f.is_file() and "__pycache__" not in str(f)))
    else:
        files_to_validate = [Path(f) for f in args.files]
    
    print(f"Validating {len(files_to_validate)} files...")
    
    total_issues = 0
    failed_files = 0
    
    for file_path in files_to_validate:
        print(f"  Validating {file_path}...")
        results = validator.validate_file(file_path)
        all_results.extend(results)
        
        file_issues = sum(len(r.issues) for r in results)
        file_failed = any(not r.passed for r in results)
        
        if file_failed:
            failed_files += 1
        
        total_issues += file_issues
        
        # Print issues for this file
        for result in results:
            if result.issues:
                print(f"    {result.element_type} '{result.element_name}':")
                for issue in result.issues:
                    print(f"      {issue.severity.upper()}: {issue.message}")
                    if issue.suggestion:
                        print(f"        Suggestion: {issue.suggestion}")
    
    # Summary
    print(f"\n📊 Validation Summary:")
    print(f"  Files validated: {len(files_to_validate)}")
    print(f"  Files with issues: {failed_files}")
    print(f"  Total issues: {total_issues}")
    print(f"  Average score: {sum(r.score for r in all_results) / len(all_results):.2f}")
    
    # Exit with error if strict mode and issues found
    if args.strict and total_issues > 0:
        print(f"\n❌ Validation failed in strict mode ({total_issues} issues)")
        sys.exit(1)
    elif total_issues == 0:
        print(f"\n✅ All docstrings passed validation!")
    else:
        print(f"\n⚠️  Validation completed with {total_issues} issues")


if __name__ == "__main__":
    main()
