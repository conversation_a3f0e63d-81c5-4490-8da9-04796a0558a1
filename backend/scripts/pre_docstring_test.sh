#!/bin/bash
"""
Pre-implementation test suite for docstring management.

This script establishes baseline metrics before docstring implementation
to ensure no functionality is broken during the process.
"""

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
BASELINE_DIR="$BACKEND_DIR/.baseline"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${BLUE}🧪 Pre-Implementation Test Suite${NC}"
echo -e "${BLUE}=================================${NC}"
echo "Project Root: $PROJECT_ROOT"
echo "Backend Dir: $BACKEND_DIR"
echo "Timestamp: $TIMESTAMP"
echo ""

# Create baseline directory
mkdir -p "$BASELINE_DIR"

cd "$BACKEND_DIR"

echo -e "${YELLOW}📋 Step 1: Environment Validation${NC}"
echo "Checking Python environment..."

# Check Python version
python_version=$(python --version 2>&1)
echo "Python Version: $python_version"

# Check virtual environment
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "Virtual Environment: $VIRTUAL_ENV"
else
    echo -e "${YELLOW}Warning: No virtual environment detected${NC}"
fi

# Check required packages
echo "Checking required packages..."
required_packages=("pytest" "mypy" "bandit" "coverage")
for package in "${required_packages[@]}"; do
    if python -c "import $package" 2>/dev/null; then
        echo "  ✅ $package installed"
    else
        echo -e "  ${RED}❌ $package not installed${NC}"
        echo "Installing $package..."
        pip install "$package"
    fi
done

echo ""

echo -e "${YELLOW}📊 Step 2: Baseline Test Coverage${NC}"
echo "Running comprehensive test suite..."

# Run tests with coverage
echo "Running pytest with coverage..."
pytest tests/ \
    --cov=app \
    --cov-report=json \
    --cov-report=html \
    --cov-report=term \
    --json-report \
    --json-report-file="$BASELINE_DIR/test_results_baseline.json" \
    -v \
    > "$BASELINE_DIR/pytest_output_baseline.txt" 2>&1

test_exit_code=$?

if [ $test_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ All tests passed${NC}"
else
    echo -e "  ${RED}❌ Some tests failed (exit code: $test_exit_code)${NC}"
    echo "  Check $BASELINE_DIR/pytest_output_baseline.txt for details"
fi

# Save coverage data
if [ -f "coverage.json" ]; then
    cp "coverage.json" "$BASELINE_DIR/coverage_baseline.json"
    echo "  📊 Coverage data saved"
fi

if [ -d "htmlcov" ]; then
    cp -r "htmlcov" "$BASELINE_DIR/htmlcov_baseline"
    echo "  📊 HTML coverage report saved"
fi

echo ""

echo -e "${YELLOW}🔍 Step 3: Type Checking Baseline${NC}"
echo "Running mypy type checking..."

mypy app/ \
    --json-report "$BASELINE_DIR/mypy_baseline.json" \
    --html-report "$BASELINE_DIR/mypy_html_baseline" \
    --txt-report "$BASELINE_DIR/mypy_txt_baseline" \
    > "$BASELINE_DIR/mypy_output_baseline.txt" 2>&1

mypy_exit_code=$?

if [ $mypy_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ No type checking errors${NC}"
else
    echo -e "  ${YELLOW}⚠️ Type checking issues found (exit code: $mypy_exit_code)${NC}"
    echo "  Check $BASELINE_DIR/mypy_output_baseline.txt for details"
fi

echo ""

echo -e "${YELLOW}🔒 Step 4: Security Scan Baseline${NC}"
echo "Running bandit security scan..."

bandit -r app/ \
    -f json \
    -o "$BASELINE_DIR/bandit_baseline.json" \
    > "$BASELINE_DIR/bandit_output_baseline.txt" 2>&1

bandit_exit_code=$?

if [ $bandit_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ No security issues found${NC}"
else
    echo -e "  ${YELLOW}⚠️ Security issues found (exit code: $bandit_exit_code)${NC}"
    echo "  Check $BASELINE_DIR/bandit_output_baseline.txt for details"
fi

echo ""

echo -e "${YELLOW}⚡ Step 5: Performance Baseline${NC}"
echo "Establishing performance baseline..."

# Create performance test script
cat > "$BASELINE_DIR/performance_test.py" << 'EOF'
#!/usr/bin/env python3
"""Performance baseline test for docstring implementation."""

import time
import json
import sys
from pathlib import Path

def test_import_performance():
    """Test import performance of main modules."""
    start_time = time.time()
    
    try:
        import app.main
        import app.core.security
        import app.api.v1.auth
        import app.services.auth_service
    except ImportError as e:
        print(f"Import error: {e}")
        return None
    
    end_time = time.time()
    return end_time - start_time

def test_startup_performance():
    """Test application startup performance."""
    start_time = time.time()
    
    try:
        from app.main import app
        # Simulate startup operations
        _ = app.routes
    except Exception as e:
        print(f"Startup error: {e}")
        return None
    
    end_time = time.time()
    return end_time - start_time

def main():
    """Run performance baseline tests."""
    results = {}
    
    print("Running performance baseline tests...")
    
    # Test import performance
    import_time = test_import_performance()
    if import_time is not None:
        results['import_time'] = import_time
        print(f"  Import time: {import_time:.3f}s")
    
    # Test startup performance
    startup_time = test_startup_performance()
    if startup_time is not None:
        results['startup_time'] = startup_time
        print(f"  Startup time: {startup_time:.3f}s")
    
    # Save results
    baseline_file = Path(__file__).parent / "performance_baseline.json"
    with open(baseline_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"Performance baseline saved to {baseline_file}")
    return 0

if __name__ == "__main__":
    sys.exit(main())
EOF

# Run performance baseline
python "$BASELINE_DIR/performance_test.py"

echo ""

echo -e "${YELLOW}📝 Step 6: Docstring Coverage Baseline${NC}"
echo "Analyzing current docstring coverage..."

# Run docstring analyzer to establish baseline
python scripts/docstring_analyzer.py \
    --project-root "$PROJECT_ROOT" \
    --output "$BASELINE_DIR/docstring_baseline_report.txt" \
    --json > "$BASELINE_DIR/docstring_baseline.json"

echo "  📊 Docstring baseline analysis complete"

echo ""

echo -e "${YELLOW}🏗️ Step 7: Build and Documentation Tests${NC}"
echo "Testing documentation build..."

# Test Sphinx documentation build
cd "$PROJECT_ROOT/docs"
sphinx-build -b html . "_build/baseline_test" \
    > "$BASELINE_DIR/sphinx_build_baseline.txt" 2>&1

sphinx_exit_code=$?

if [ $sphinx_exit_code -eq 0 ]; then
    echo -e "  ${GREEN}✅ Documentation builds successfully${NC}"
else
    echo -e "  ${YELLOW}⚠️ Documentation build issues (exit code: $sphinx_exit_code)${NC}"
    echo "  Check $BASELINE_DIR/sphinx_build_baseline.txt for details"
fi

cd "$BACKEND_DIR"

echo ""

echo -e "${YELLOW}📋 Step 8: Baseline Summary Generation${NC}"
echo "Generating comprehensive baseline summary..."

# Create baseline summary
cat > "$BASELINE_DIR/baseline_summary.json" << EOF
{
  "timestamp": "$TIMESTAMP",
  "test_results": {
    "pytest_exit_code": $test_exit_code,
    "mypy_exit_code": $mypy_exit_code,
    "bandit_exit_code": $bandit_exit_code,
    "sphinx_exit_code": $sphinx_exit_code
  },
  "files_created": [
    "test_results_baseline.json",
    "coverage_baseline.json",
    "mypy_baseline.json",
    "bandit_baseline.json",
    "performance_baseline.json",
    "docstring_baseline.json",
    "docstring_baseline_report.txt",
    "sphinx_build_baseline.txt"
  ],
  "baseline_directory": "$BASELINE_DIR"
}
EOF

# Create human-readable summary
cat > "$BASELINE_DIR/BASELINE_SUMMARY.md" << EOF
# Pre-Implementation Baseline Summary

**Generated:** $(date)
**Timestamp:** $TIMESTAMP

## Test Results Summary

| Test Type | Status | Exit Code | Notes |
|-----------|--------|-----------|-------|
| PyTest | $([ $test_exit_code -eq 0 ] && echo "✅ PASS" || echo "❌ FAIL") | $test_exit_code | Comprehensive test suite |
| MyPy | $([ $mypy_exit_code -eq 0 ] && echo "✅ PASS" || echo "⚠️ ISSUES") | $mypy_exit_code | Type checking |
| Bandit | $([ $bandit_exit_code -eq 0 ] && echo "✅ PASS" || echo "⚠️ ISSUES") | $bandit_exit_code | Security scanning |
| Sphinx | $([ $sphinx_exit_code -eq 0 ] && echo "✅ PASS" || echo "⚠️ ISSUES") | $sphinx_exit_code | Documentation build |

## Baseline Files Created

- \`test_results_baseline.json\` - Complete test results
- \`coverage_baseline.json\` - Code coverage data
- \`mypy_baseline.json\` - Type checking results
- \`bandit_baseline.json\` - Security scan results
- \`performance_baseline.json\` - Performance metrics
- \`docstring_baseline.json\` - Current docstring coverage
- \`docstring_baseline_report.txt\` - Detailed docstring analysis

## Next Steps

1. **Review Baseline Results**: Check all baseline files for any critical issues
2. **Begin Implementation**: Start docstring implementation following the PRD
3. **Monitor Progress**: Use \`docstring_tracker.py\` to track progress
4. **Post-Implementation Testing**: Run \`post_docstring_test.sh\` after changes

## Validation Commands

\`\`\`bash
# Compare test results after implementation
python scripts/compare_baselines.py

# Run post-implementation validation
./scripts/post_docstring_test.sh

# Check progress
python scripts/docstring_tracker.py --dashboard
\`\`\`

## Baseline Directory

All baseline files are stored in: \`$BASELINE_DIR\`
EOF

echo -e "  ${GREEN}✅ Baseline summary generated${NC}"

echo ""

echo -e "${GREEN}🎉 Pre-Implementation Baseline Complete!${NC}"
echo -e "${GREEN}=======================================${NC}"
echo ""
echo "📊 Summary:"
echo "  • Test Suite: $([ $test_exit_code -eq 0 ] && echo "✅ PASSED" || echo "❌ FAILED")"
echo "  • Type Checking: $([ $mypy_exit_code -eq 0 ] && echo "✅ PASSED" || echo "⚠️ ISSUES")"
echo "  • Security Scan: $([ $bandit_exit_code -eq 0 ] && echo "✅ PASSED" || echo "⚠️ ISSUES")"
echo "  • Documentation: $([ $sphinx_exit_code -eq 0 ] && echo "✅ PASSED" || echo "⚠️ ISSUES")"
echo ""
echo "📁 Baseline files saved to: $BASELINE_DIR"
echo "📋 Review summary: $BASELINE_DIR/BASELINE_SUMMARY.md"
echo ""
echo "🚀 Ready to begin docstring implementation!"
echo ""
echo "Next steps:"
echo "  1. Review baseline results for any critical issues"
echo "  2. Begin implementing docstrings following the PRD"
echo "  3. Run post-implementation tests: ./scripts/post_docstring_test.sh"
echo ""

# Exit with appropriate code
if [ $test_exit_code -eq 0 ]; then
    exit 0
else
    echo -e "${RED}⚠️ Some baseline tests failed. Review before proceeding.${NC}"
    exit 1
fi
