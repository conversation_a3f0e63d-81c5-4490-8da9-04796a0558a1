Discovery Orchestration API
============================

The Discovery Orchestration API provides comprehensive endpoints for managing multi-cloud asset discovery operations, job orchestration, and automated discovery workflows within the Blast-Radius Security Tool.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Discovery Orchestration API supports:

* **Multi-Cloud Discovery**: Automated asset discovery across AWS, Azure, and GCP
* **API Discovery**: Specialized API endpoint discovery using tools like Akto and Kiterunner
* **Network Discovery**: Network-based asset discovery using nmap and other tools
* **Job Management**: Complete discovery job lifecycle management
* **Result Processing**: Automated asset and relationship creation from discovery results

Base URL
--------

All discovery endpoints are available under::

    /api/v1/discovery/

Authentication
--------------

All discovery endpoints require authentication and appropriate permissions::

    Authorization: Bearer <jwt-token>

Discovery Job Management
------------------------

**Create Discovery Job**

.. code-block:: http

    POST /api/v1/discovery/jobs
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "name": "Production AWS Discovery",
        "description": "Discover all AWS resources in production environment",
        "job_type": "aws_discovery",
        "configuration": {
            "region": "us-east-1",
            "services": ["ec2", "rds", "s3", "lambda"],
            "credentials_profile": "production",
            "include_tags": true,
            "scan_security_groups": true
        },
        "schedule": {
            "enabled": true,
            "cron_expression": "0 2 * * *",
            "timezone": "UTC"
        }
    }

**Response:**

.. code-block:: json

    {
        "job_id": "job_12345",
        "name": "Production AWS Discovery",
        "status": "created",
        "job_type": "aws_discovery",
        "created_at": "2024-06-17T15:30:00Z",
        "estimated_duration": "15 minutes",
        "next_run": "2024-06-18T02:00:00Z"
    }

**List Discovery Jobs**

.. code-block:: http

    GET /api/v1/discovery/jobs
    Authorization: Bearer <token>

Query Parameters:

* ``job_type`` (string, optional): Filter by job type
* ``status`` (string, optional): Filter by job status
* ``page`` (integer, optional): Page number (default: 1)
* ``size`` (integer, optional): Page size (default: 20, max: 100)

**Response:**

.. code-block:: json

    {
        "jobs": [
            {
                "job_id": "job_12345",
                "name": "Production AWS Discovery",
                "job_type": "aws_discovery",
                "status": "completed",
                "created_at": "2024-06-17T15:30:00Z",
                "started_at": "2024-06-17T15:31:00Z",
                "completed_at": "2024-06-17T15:45:00Z",
                "duration_seconds": 840,
                "assets_discovered": 156,
                "assets_updated": 23,
                "relationships_discovered": 89,
                "errors_count": 0
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 45,
            "has_more": true
        }
    }

**Get Job Details**

.. code-block:: http

    GET /api/v1/discovery/jobs/{job_id}
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "job_id": "job_12345",
        "name": "Production AWS Discovery",
        "description": "Discover all AWS resources in production environment",
        "job_type": "aws_discovery",
        "status": "completed",
        "configuration": {
            "region": "us-east-1",
            "services": ["ec2", "rds", "s3", "lambda"],
            "credentials_profile": "production"
        },
        "created_at": "2024-06-17T15:30:00Z",
        "started_at": "2024-06-17T15:31:00Z",
        "completed_at": "2024-06-17T15:45:00Z",
        "duration_seconds": 840,
        "results": {
            "assets_discovered": 156,
            "assets_updated": 23,
            "relationships_discovered": 89,
            "errors_count": 0
        },
        "execution_log": [
            {
                "timestamp": "2024-06-17T15:31:00Z",
                "level": "INFO",
                "message": "Starting AWS discovery for region us-east-1"
            },
            {
                "timestamp": "2024-06-17T15:32:15Z",
                "level": "INFO",
                "message": "Discovered 45 EC2 instances"
            }
        ]
    }

**Start Discovery Job**

.. code-block:: http

    POST /api/v1/discovery/jobs/{job_id}/start
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "job_id": "job_12345",
        "status": "running",
        "started_at": "2024-06-17T15:30:00Z",
        "estimated_completion": "2024-06-17T15:45:00Z"
    }

**Cancel Discovery Job**

.. code-block:: http

    POST /api/v1/discovery/jobs/{job_id}/cancel
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "job_id": "job_12345",
        "status": "cancelled",
        "cancelled_at": "2024-06-17T15:35:00Z",
        "partial_results": {
            "assets_discovered": 67,
            "relationships_discovered": 34
        }
    }

Cloud-Specific Discovery
------------------------

**AWS Discovery**

.. code-block:: http

    POST /api/v1/discovery/cloud/aws
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "region": "us-west-2",
        "services": ["ec2", "rds", "s3"],
        "credentials_profile": "production",
        "include_tags": true,
        "scan_security_groups": true,
        "discover_relationships": true
    }

**Response:**

.. code-block:: json

    {
        "job_id": "aws_job_67890",
        "status": "running",
        "discovery_type": "aws_discovery",
        "region": "us-west-2",
        "started_at": "2024-06-17T15:30:00Z",
        "estimated_completion": "2024-06-17T15:45:00Z"
    }

**Azure Discovery**

.. code-block:: http

    POST /api/v1/discovery/cloud/azure
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "subscription_id": "********-1234-1234-1234-************",
        "resource_groups": ["production", "staging"],
        "services": ["virtual_machines", "storage_accounts", "sql_databases"],
        "include_network_topology": true
    }

**GCP Discovery**

.. code-block:: http

    POST /api/v1/discovery/cloud/gcp
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "project_id": "my-production-project",
        "zones": ["us-central1-a", "us-central1-b"],
        "services": ["compute", "storage", "sql"],
        "service_account_key": "path/to/service-account.json"
    }

API Discovery
-------------

**Akto API Discovery**

.. code-block:: http

    POST /api/v1/discovery/api/akto
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "target_domains": ["api.example.com", "internal-api.company.com"],
        "authentication": {
            "type": "bearer_token",
            "token": "api-token-here"
        },
        "scan_depth": "deep",
        "include_swagger": true,
        "rate_limit": 10
    }

**Response:**

.. code-block:: json

    {
        "job_id": "akto_job_11111",
        "status": "running",
        "discovery_type": "akto_discovery",
        "targets": ["api.example.com", "internal-api.company.com"],
        "started_at": "2024-06-17T15:30:00Z",
        "estimated_completion": "2024-06-17T16:00:00Z"
    }

**Kiterunner API Discovery**

.. code-block:: http

    POST /api/v1/discovery/api/kiterunner
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "target_urls": ["https://api.example.com"],
        "wordlists": ["common_apis", "rest_endpoints"],
        "threads": 20,
        "delay": 100,
        "include_response_analysis": true
    }

Network Discovery
-----------------

**Network Scan**

.. code-block:: http

    POST /api/v1/discovery/network/scan
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "targets": ["***********/24", "10.0.0.0/16"],
        "scan_type": "comprehensive",
        "port_ranges": ["1-1000", "3389", "5432"],
        "service_detection": true,
        "os_detection": true,
        "vulnerability_scan": false
    }

**Response:**

.. code-block:: json

    {
        "job_id": "network_job_22222",
        "status": "running",
        "discovery_type": "network_discovery",
        "targets": ["***********/24", "10.0.0.0/16"],
        "started_at": "2024-06-17T15:30:00Z",
        "estimated_completion": "2024-06-17T16:15:00Z"
    }

Discovery Results
-----------------

**Get Discovery Results**

.. code-block:: http

    GET /api/v1/discovery/jobs/{job_id}/results
    Authorization: Bearer <token>

Query Parameters:

* ``include_assets`` (boolean, optional): Include discovered assets (default: true)
* ``include_relationships`` (boolean, optional): Include relationships (default: true)
* ``include_errors`` (boolean, optional): Include error details (default: false)

**Response:**

.. code-block:: json

    {
        "job_id": "job_12345",
        "results": {
            "summary": {
                "assets_discovered": 156,
                "assets_updated": 23,
                "relationships_discovered": 89,
                "errors_count": 2
            },
            "assets": [
                {
                    "asset_id": "asset_001",
                    "name": "web-server-prod-01",
                    "asset_type": "server",
                    "provider": "aws",
                    "provider_id": "i-********90abcdef0",
                    "ip_address": "**********",
                    "metadata": {
                        "instance_type": "t3.medium",
                        "availability_zone": "us-east-1a",
                        "tags": {
                            "Environment": "production",
                            "Application": "web-frontend"
                        }
                    },
                    "discovered_at": "2024-06-17T15:32:00Z"
                }
            ],
            "relationships": [
                {
                    "relationship_id": "rel_001",
                    "source_asset_id": "asset_001",
                    "target_asset_id": "asset_002",
                    "relationship_type": "connects_to",
                    "protocol": "tcp",
                    "port": 5432,
                    "discovered_at": "2024-06-17T15:33:00Z"
                }
            ],
            "errors": [
                {
                    "timestamp": "2024-06-17T15:35:00Z",
                    "error_type": "permission_denied",
                    "message": "Access denied to S3 bucket: private-bucket",
                    "resource": "s3://private-bucket"
                }
            ]
        }
    }

Discovery Statistics
--------------------

**Get Discovery Statistics**

.. code-block:: http

    GET /api/v1/discovery/statistics
    Authorization: Bearer <token>

Query Parameters:

* ``period`` (string, optional): Time period (day, week, month) (default: week)
* ``job_type`` (string, optional): Filter by job type

**Response:**

.. code-block:: json

    {
        "statistics": {
            "period": "week",
            "total_jobs": 45,
            "successful_jobs": 42,
            "failed_jobs": 3,
            "average_duration": 720,
            "total_assets_discovered": 2340,
            "total_relationships_discovered": 1567,
            "job_types": {
                "aws_discovery": 25,
                "azure_discovery": 12,
                "network_discovery": 8
            },
            "discovery_trends": [
                {
                    "date": "2024-06-17",
                    "jobs_completed": 8,
                    "assets_discovered": 234,
                    "success_rate": 0.95
                }
            ]
        }
    }

Scheduled Discovery
-------------------

**Create Scheduled Discovery**

.. code-block:: http

    POST /api/v1/discovery/schedules
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "name": "Daily AWS Production Scan",
        "job_template": {
            "job_type": "aws_discovery",
            "configuration": {
                "region": "us-east-1",
                "services": ["ec2", "rds"]
            }
        },
        "schedule": {
            "cron_expression": "0 2 * * *",
            "timezone": "UTC",
            "enabled": true
        },
        "notifications": {
            "on_success": ["<EMAIL>"],
            "on_failure": ["<EMAIL>"]
        }
    }

**Response:**

.. code-block:: json

    {
        "schedule_id": "sched_12345",
        "name": "Daily AWS Production Scan",
        "status": "active",
        "next_run": "2024-06-18T02:00:00Z",
        "created_at": "2024-06-17T15:30:00Z"
    }

**List Scheduled Discoveries**

.. code-block:: http

    GET /api/v1/discovery/schedules
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "schedules": [
            {
                "schedule_id": "sched_12345",
                "name": "Daily AWS Production Scan",
                "status": "active",
                "cron_expression": "0 2 * * *",
                "next_run": "2024-06-18T02:00:00Z",
                "last_run": "2024-06-17T02:00:00Z",
                "last_run_status": "success"
            }
        ]
    }

Error Handling
--------------

**Common Error Responses:**

**400 Bad Request - Invalid Configuration:**

.. code-block:: json

    {
        "error": {
            "code": "INVALID_CONFIGURATION",
            "message": "Invalid discovery configuration",
            "details": {
                "field": "region",
                "issue": "Region 'invalid-region' is not supported"
            }
        }
    }

**403 Forbidden - Insufficient Permissions:**

.. code-block:: json

    {
        "error": {
            "code": "INSUFFICIENT_PERMISSIONS",
            "message": "Insufficient permissions for discovery operation",
            "details": {
                "required_permission": "DISCOVERY_EXECUTE",
                "resource": "aws_discovery"
            }
        }
    }

**409 Conflict - Job Already Running:**

.. code-block:: json

    {
        "error": {
            "code": "JOB_ALREADY_RUNNING",
            "message": "A discovery job is already running for this target",
            "details": {
                "existing_job_id": "job_67890",
                "started_at": "2024-06-17T15:00:00Z"
            }
        }
    }

Best Practices
--------------

1. **Job Naming**: Use descriptive names for discovery jobs
2. **Configuration Validation**: Validate configurations before starting jobs
3. **Resource Limits**: Respect rate limits and resource constraints
4. **Error Handling**: Implement proper error handling and retry logic
5. **Monitoring**: Monitor job progress and handle failures gracefully
6. **Scheduling**: Use appropriate scheduling for regular discoveries
7. **Security**: Secure credential storage and access controls

Integration Examples
--------------------

**Python SDK Example:**

.. code-block:: python

    from blast_radius import BlastRadiusClient
    
    client = BlastRadiusClient(api_token="your-token")
    
    # Create and start AWS discovery
    job = client.discovery.create_job(
        name="Production AWS Discovery",
        job_type="aws_discovery",
        configuration={
            "region": "us-east-1",
            "services": ["ec2", "rds", "s3"]
        }
    )
    
    # Start the job
    client.discovery.start_job(job.job_id)
    
    # Monitor progress
    while True:
        status = client.discovery.get_job_status(job.job_id)
        if status.status in ["completed", "failed"]:
            break
        time.sleep(30)
    
    # Get results
    results = client.discovery.get_job_results(job.job_id)
    print(f"Discovered {results.assets_discovered} assets")

This comprehensive API enables full discovery orchestration and automation capabilities.
