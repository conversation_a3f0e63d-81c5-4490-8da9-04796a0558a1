Real-time Monitoring API
=========================

The Real-time Monitoring API provides WebSocket-based live monitoring, dashboard updates, threat visualization, and event streaming capabilities for the Blast-Radius Security Tool.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Real-time Monitoring API supports:

* **WebSocket Connections**: Persistent connections for real-time data streaming
* **Dashboard Updates**: Live dashboard metrics and visualization updates
* **Threat Map Visualization**: Geographic threat distribution and attack patterns
* **Event Streaming**: Real-time security event notifications
* **Attack Timeline**: Live attack progression and MITRE ATT&CK correlation

Base URL
--------

WebSocket connections::

    ws://your-instance.com/api/v1/realtime/ws

HTTP endpoints::

    /api/v1/realtime/

Authentication
--------------

WebSocket authentication uses JWT tokens in the connection URL::

    ws://your-instance.com/api/v1/realtime/ws?token=<jwt-token>

HTTP endpoints require standard Bearer authentication::

    Authorization: Bearer <jwt-token>

WebSocket Connection
--------------------

**Establishing Connection**

.. code-block:: javascript

    const ws = new WebSocket('ws://localhost:8000/api/v1/realtime/ws?token=your-jwt-token');
    
    ws.onopen = function(event) {
        console.log('Connected to real-time monitoring');
        
        // Subscribe to dashboard updates
        ws.send(JSON.stringify({
            type: 'subscribe',
            channels: ['dashboard', 'threats', 'attacks']
        }));
    };
    
    ws.onmessage = function(event) {
        const data = JSON.parse(event.data);
        handleRealtimeUpdate(data);
    };

**Message Format**

All WebSocket messages follow a consistent format:

.. code-block:: json

    {
        "type": "event_type",
        "timestamp": "2024-06-17T15:30:00Z",
        "channel": "dashboard",
        "data": {
            // Event-specific data
        }
    }

**Subscription Management**

.. code-block:: javascript

    // Subscribe to specific channels
    ws.send(JSON.stringify({
        type: 'subscribe',
        channels: ['dashboard', 'threats', 'attacks', 'compliance']
    }));
    
    // Unsubscribe from channels
    ws.send(JSON.stringify({
        type: 'unsubscribe',
        channels: ['attacks']
    }));
    
    // Get current subscriptions
    ws.send(JSON.stringify({
        type: 'get_subscriptions'
    }));

Dashboard Updates
-----------------

**Dashboard Overview**

Real-time dashboard metrics are streamed via WebSocket:

.. code-block:: json

    {
        "type": "dashboard_update",
        "timestamp": "2024-06-17T15:30:00Z",
        "channel": "dashboard",
        "data": {
            "total_assets": 15420,
            "critical_vulnerabilities": 23,
            "active_threats": 7,
            "attack_paths_discovered": 156,
            "risk_score": 72.5,
            "compliance_status": "compliant",
            "last_discovery": "2024-06-17T14:45:00Z",
            "system_health": "healthy"
        }
    }

**HTTP Endpoint for Dashboard Data**

.. code-block:: http

    GET /api/v1/realtime/dashboard/overview
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "dashboard_data": {
            "metrics": {
                "total_assets": 15420,
                "critical_vulnerabilities": 23,
                "active_threats": 7,
                "attack_paths_discovered": 156
            },
            "risk_assessment": {
                "overall_risk_score": 72.5,
                "risk_trend": "decreasing",
                "critical_assets_at_risk": 12
            },
            "compliance": {
                "status": "compliant",
                "frameworks": ["GDPR", "SOX", "HIPAA"],
                "last_audit": "2024-06-15T10:00:00Z"
            },
            "system_status": {
                "health": "healthy",
                "uptime": "99.97%",
                "last_discovery": "2024-06-17T14:45:00Z"
            }
        }
    }

Threat Map Visualization
------------------------

**Geographic Threat Distribution**

.. code-block:: http

    GET /api/v1/realtime/threat-map
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "threat_map_data": {
            "threats_by_location": [
                {
                    "country": "US",
                    "latitude": 39.8283,
                    "longitude": -98.5795,
                    "threat_count": 45,
                    "severity": "high",
                    "threat_types": ["malware", "phishing", "apt"]
                },
                {
                    "country": "CN",
                    "latitude": 35.8617,
                    "longitude": 104.1954,
                    "threat_count": 23,
                    "severity": "medium",
                    "threat_types": ["scanning", "brute_force"]
                }
            ],
            "attack_vectors": [
                {
                    "source_country": "RU",
                    "target_country": "US",
                    "attack_count": 12,
                    "primary_technique": "T1566.001"
                }
            ],
            "last_updated": "2024-06-17T15:30:00Z"
        }
    }

**Real-time Threat Updates**

WebSocket messages for new threats:

.. code-block:: json

    {
        "type": "threat_detected",
        "timestamp": "2024-06-17T15:30:00Z",
        "channel": "threats",
        "data": {
            "threat_id": "threat_12345",
            "source_ip": "*************",
            "target_asset": "web-server-01",
            "threat_type": "malware",
            "severity": "high",
            "mitre_technique": "T1055",
            "location": {
                "country": "US",
                "latitude": 39.8283,
                "longitude": -98.5795
            },
            "confidence": 0.95
        }
    }

Risk Heatmap Data
-----------------

**Asset Risk Heatmap**

.. code-block:: http

    GET /api/v1/realtime/risk-heatmap
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "heatmap_data": {
            "assets": [
                {
                    "asset_id": "web-server-01",
                    "name": "Production Web Server",
                    "risk_score": 85.2,
                    "criticality": "high",
                    "vulnerability_count": 7,
                    "attack_paths": 12,
                    "position": {
                        "x": 150,
                        "y": 200
                    }
                }
            ],
            "risk_zones": [
                {
                    "zone_id": "dmz",
                    "name": "DMZ Network",
                    "average_risk": 72.5,
                    "asset_count": 25,
                    "bounds": {
                        "x1": 100, "y1": 150,
                        "x2": 300, "y2": 250
                    }
                }
            ],
            "last_updated": "2024-06-17T15:30:00Z"
        }
    }

Attack Timeline
---------------

**Real-time Attack Progression**

.. code-block:: http

    GET /api/v1/realtime/attack-timeline
    Authorization: Bearer <token>

Query Parameters:

* ``hours`` (integer, optional): Hours of history to include (default: 24)
* ``severity`` (string, optional): Filter by severity level
* ``asset_id`` (string, optional): Filter by specific asset

**Response:**

.. code-block:: json

    {
        "timeline_data": {
            "events": [
                {
                    "event_id": "evt_001",
                    "timestamp": "2024-06-17T15:25:00Z",
                    "event_type": "initial_access",
                    "mitre_technique": "T1566.001",
                    "technique_name": "Spearphishing Attachment",
                    "asset_id": "workstation-05",
                    "severity": "high",
                    "description": "Malicious email attachment executed",
                    "indicators": ["file_hash:abc123", "email:<EMAIL>"]
                },
                {
                    "event_id": "evt_002",
                    "timestamp": "2024-06-17T15:27:00Z",
                    "event_type": "privilege_escalation",
                    "mitre_technique": "T1055",
                    "technique_name": "Process Injection",
                    "asset_id": "workstation-05",
                    "severity": "high",
                    "description": "Process injection detected",
                    "parent_event": "evt_001"
                }
            ],
            "attack_chains": [
                {
                    "chain_id": "chain_001",
                    "events": ["evt_001", "evt_002"],
                    "start_time": "2024-06-17T15:25:00Z",
                    "status": "active",
                    "predicted_next_steps": ["T1083", "T1005"]
                }
            ]
        }
    }

**WebSocket Attack Updates**

.. code-block:: json

    {
        "type": "attack_progression",
        "timestamp": "2024-06-17T15:30:00Z",
        "channel": "attacks",
        "data": {
            "event_id": "evt_003",
            "chain_id": "chain_001",
            "mitre_technique": "T1083",
            "technique_name": "File and Directory Discovery",
            "asset_id": "workstation-05",
            "severity": "medium",
            "confidence": 0.87,
            "next_predicted": ["T1005", "T1039"]
        }
    }

MITRE ATT&CK Matrix Visualization
---------------------------------

**Live Technique Usage**

.. code-block:: http

    GET /api/v1/realtime/mitre-matrix
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "matrix_data": {
            "techniques": [
                {
                    "technique_id": "T1566.001",
                    "name": "Spearphishing Attachment",
                    "tactic": "initial-access",
                    "usage_count": 15,
                    "last_seen": "2024-06-17T15:25:00Z",
                    "severity": "high",
                    "heat_level": 0.85
                }
            ],
            "tactics": [
                {
                    "tactic": "initial-access",
                    "technique_count": 8,
                    "active_techniques": 3,
                    "heat_level": 0.72
                }
            ],
            "last_updated": "2024-06-17T15:30:00Z"
        }
    }

System Metrics
--------------

**Performance Monitoring**

.. code-block:: http

    GET /api/v1/realtime/system-metrics
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "system_metrics": {
            "performance": {
                "cpu_usage": 45.2,
                "memory_usage": 67.8,
                "disk_usage": 34.1,
                "network_io": {
                    "bytes_in": 1024000,
                    "bytes_out": 512000
                }
            },
            "discovery_jobs": {
                "active": 3,
                "queued": 1,
                "completed_today": 12,
                "failed_today": 0
            },
            "analysis_engine": {
                "graphs_processed": 156,
                "paths_analyzed": 2340,
                "average_response_time": 0.85
            },
            "database": {
                "connections": 45,
                "query_performance": 0.12,
                "storage_used": "2.3TB"
            }
        }
    }

Event Streaming
---------------

**Security Event Stream**

WebSocket messages for security events:

.. code-block:: json

    {
        "type": "security_event",
        "timestamp": "2024-06-17T15:30:00Z",
        "channel": "events",
        "data": {
            "event_id": "sec_001",
            "event_type": "vulnerability_discovered",
            "asset_id": "web-server-01",
            "severity": "high",
            "cve_id": "CVE-2024-1234",
            "cvss_score": 8.5,
            "description": "Remote code execution vulnerability",
            "remediation": "Apply security patch immediately"
        }
    }

**Compliance Event Stream**

.. code-block:: json

    {
        "type": "compliance_event",
        "timestamp": "2024-06-17T15:30:00Z",
        "channel": "compliance",
        "data": {
            "event_id": "comp_001",
            "event_type": "data_subject_request",
            "request_id": "dsr_12345",
            "request_type": "ACCESS",
            "status": "submitted",
            "due_date": "2024-06-24T15:30:00Z"
        }
    }

Client Libraries
----------------

**JavaScript Client**

.. code-block:: javascript

    class BlastRadiusRealtimeClient {
        constructor(wsUrl, token) {
            this.wsUrl = wsUrl;
            this.token = token;
            this.ws = null;
            this.subscriptions = new Set();
        }
        
        connect() {
            this.ws = new WebSocket(`${this.wsUrl}?token=${this.token}`);
            
            this.ws.onopen = () => {
                console.log('Connected to real-time monitoring');
                this.resubscribe();
            };
            
            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            };
        }
        
        subscribe(channels) {
            channels.forEach(channel => this.subscriptions.add(channel));
            this.ws.send(JSON.stringify({
                type: 'subscribe',
                channels: Array.from(this.subscriptions)
            }));
        }
        
        handleMessage(data) {
            switch(data.type) {
                case 'dashboard_update':
                    this.updateDashboard(data.data);
                    break;
                case 'threat_detected':
                    this.showThreatAlert(data.data);
                    break;
                case 'attack_progression':
                    this.updateAttackTimeline(data.data);
                    break;
            }
        }
    }

**Python Client**

.. code-block:: python

    import asyncio
    import websockets
    import json
    
    class BlastRadiusRealtimeClient:
        def __init__(self, ws_url, token):
            self.ws_url = ws_url
            self.token = token
            self.subscriptions = set()
        
        async def connect(self):
            uri = f"{self.ws_url}?token={self.token}"
            async with websockets.connect(uri) as websocket:
                await self.resubscribe(websocket)
                async for message in websocket:
                    data = json.loads(message)
                    await self.handle_message(data)
        
        async def subscribe(self, websocket, channels):
            self.subscriptions.update(channels)
            await websocket.send(json.dumps({
                'type': 'subscribe',
                'channels': list(self.subscriptions)
            }))
        
        async def handle_message(self, data):
            if data['type'] == 'dashboard_update':
                await self.update_dashboard(data['data'])
            elif data['type'] == 'threat_detected':
                await self.handle_threat(data['data'])

Error Handling
--------------

**WebSocket Errors**

.. code-block:: json

    {
        "type": "error",
        "timestamp": "2024-06-17T15:30:00Z",
        "error": {
            "code": "SUBSCRIPTION_FAILED",
            "message": "Failed to subscribe to channel",
            "details": {
                "channel": "invalid_channel",
                "reason": "Channel does not exist"
            }
        }
    }

**Connection Management**

.. code-block:: javascript

    ws.onclose = function(event) {
        if (event.code !== 1000) {
            // Unexpected close, attempt reconnection
            setTimeout(() => {
                console.log('Attempting to reconnect...');
                connect();
            }, 5000);
        }
    };
    
    ws.onerror = function(error) {
        console.error('WebSocket error:', error);
    };

Best Practices
--------------

1. **Connection Management**: Implement automatic reconnection with exponential backoff
2. **Subscription Management**: Only subscribe to needed channels to reduce bandwidth
3. **Error Handling**: Gracefully handle connection drops and errors
4. **Rate Limiting**: Respect WebSocket message rate limits
5. **Data Buffering**: Buffer updates for smooth UI rendering
6. **Security**: Always use secure WebSocket connections (wss://) in production

This real-time monitoring API enables comprehensive live security monitoring and visualization capabilities.
