API Reference
=============

The Blast-Radius Security Tool provides a comprehensive REST API for all security analysis operations. This section contains detailed documentation for all available endpoints, request/response formats, and integration examples.

Overview
--------

The API is organized into several functional areas:

* **Authentication & Authorization**: User management and access control
* **Asset Management**: Asset discovery, inventory, and metadata management
* **Attack Path Analysis**: Graph-based security analysis and threat modeling
* **MITRE ATT&CK Integration**: Comprehensive threat intelligence and technique correlation
* **Compliance Framework**: GDPR compliance, audit logging, and regulatory management
* **Real-time Monitoring**: WebSocket-based live monitoring and dashboard updates
* **Discovery Orchestration**: Multi-cloud asset discovery and job management
* **Threat Intelligence**: IOC management and threat correlation
* **Monitoring & Dashboards**: Real-time monitoring and visualization
* **Integrations**: Third-party system integrations

Base URL
--------

All API endpoints are available under::

    https://your-blast-radius-instance.com/api/v1/

Authentication
--------------

All API endpoints require authentication using JWT Bearer tokens::

    Authorization: Bearer <your-jwt-token>

Obtain tokens through the authentication endpoints or configure service accounts for automated access.

Rate Limiting
-------------

API requests are rate limited based on user tier:

* **Standard**: 60 requests per minute
* **Premium**: 300 requests per minute  
* **Enterprise**: 1000 requests per minute

Rate limit headers are included in all responses::

    X-RateLimit-Limit: 300
    X-RateLimit-Remaining: 245
    X-RateLimit-Reset: **********

API Endpoints
-------------

Authentication & User Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   authentication

Endpoints for user authentication, authorization, and account management.

Asset Management
~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   asset-management

Comprehensive asset discovery, inventory management, and metadata operations.

Attack Path Analysis
~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   attack-path-analysis

Advanced graph-based attack path discovery, blast radius calculation, and MITRE ATT&CK integration.

Threat Modeling & Risk Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   threat-modeling

Quantitative threat modeling, attack simulation, and regulatory compliance impact assessment.

MITRE ATT&CK Integration
~~~~~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   mitre-attack-integration

Real-time technique correlation, threat actor attribution, and attack pattern analysis with complete MITRE ATT&CK framework coverage.

Compliance Framework
~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   compliance-framework

Comprehensive GDPR compliance management, audit logging, data subject rights, and regulatory framework support.

Real-time Monitoring
~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   realtime-monitoring

WebSocket-based real-time monitoring, live dashboard updates, threat map visualization, and event streaming.

Discovery Orchestration
~~~~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 2

   discovery-orchestration

Multi-cloud asset discovery orchestration, job management, and automated discovery workflows.

Threat Intelligence
~~~~~~~~~~~~~~~~~~~

Threat intelligence management, IOC correlation, and threat actor profiling.

*Note: Detailed threat intelligence API documentation coming soon.*

Monitoring & Dashboards
~~~~~~~~~~~~~~~~~~~~~~~~

Real-time monitoring, alerting, and dashboard management endpoints.

*Note: Detailed monitoring API documentation coming soon.*

Integrations
~~~~~~~~~~~~

Third-party system integrations including SIEM, SOAR, and cloud platforms.

*Note: Detailed integrations API documentation coming soon.*

Common Response Formats
-----------------------

Success Response
~~~~~~~~~~~~~~~~

All successful API responses follow a consistent format:

.. code-block:: json

    {
        "status": "success",
        "data": {
            // Response data specific to endpoint
        },
        "message": "Operation completed successfully",
        "timestamp": "2024-12-11T22:30:00Z",
        "request_id": "req_**********"
    }

Error Response
~~~~~~~~~~~~~~

Error responses include detailed information for troubleshooting:

.. code-block:: json

    {
        "status": "error",
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Invalid request parameters",
            "details": {
                "field": "source_asset_id",
                "issue": "Asset not found"
            }
        },
        "timestamp": "2024-12-11T22:30:00Z",
        "request_id": "req_**********"
    }

HTTP Status Codes
~~~~~~~~~~~~~~~~~

The API uses standard HTTP status codes:

* **200 OK**: Request successful
* **201 Created**: Resource created successfully
* **400 Bad Request**: Invalid request parameters
* **401 Unauthorized**: Authentication required
* **403 Forbidden**: Insufficient permissions
* **404 Not Found**: Resource not found
* **409 Conflict**: Resource conflict (e.g., duplicate)
* **422 Unprocessable Entity**: Validation errors
* **423 Locked**: Resource temporarily locked
* **429 Too Many Requests**: Rate limit exceeded
* **500 Internal Server Error**: Server error
* **503 Service Unavailable**: Service temporarily unavailable

Pagination
----------

List endpoints support pagination using cursor-based pagination:

**Request Parameters:**

* ``limit`` (integer): Number of items per page (max: 100, default: 20)
* ``cursor`` (string): Pagination cursor for next page

**Response Format:**

.. code-block:: json

    {
        "data": [
            // Array of items
        ],
        "pagination": {
            "limit": 20,
            "has_more": true,
            "next_cursor": "eyJpZCI6MTIzNDU2Nzg5MH0",
            "total_count": 1250
        }
    }

Filtering and Sorting
---------------------

Many endpoints support filtering and sorting:

**Filtering:**

Use query parameters to filter results::

    GET /api/v1/assets?asset_type=server&environment=production&risk_level=high

**Sorting:**

Use the ``sort`` parameter with field names::

    GET /api/v1/assets?sort=risk_score:desc,name:asc

**Search:**

Use the ``search`` parameter for text search::

    GET /api/v1/assets?search=database

Field Selection
---------------

Optimize responses by selecting specific fields::

    GET /api/v1/assets?fields=id,name,asset_type,risk_score

This reduces response size and improves performance for large datasets.

Webhooks
--------

The API supports webhooks for real-time event notifications:

**Supported Events:**

* ``asset.discovered`` - New asset discovered
* ``asset.updated`` - Asset metadata updated
* ``attack_path.critical`` - Critical attack path identified
* ``threat.detected`` - New threat detected
* ``incident.created`` - Security incident created

**Webhook Configuration:**

.. code-block:: json

    {
        "url": "https://your-system.com/webhooks/blast-radius",
        "events": ["asset.discovered", "attack_path.critical"],
        "secret": "your-webhook-secret",
        "active": true
    }

**Webhook Payload:**

.. code-block:: json

    {
        "event": "attack_path.critical",
        "timestamp": "2024-12-11T22:30:00Z",
        "data": {
            "path_id": "path_001",
            "source_asset_id": "web_server_001",
            "target_asset_id": "database_001",
            "risk_score": 95.5,
            "criticality_level": "CRITICAL"
        },
        "signature": "sha256=..."
    }

SDK and Libraries
-----------------

Official SDKs are available for popular programming languages:

**Python SDK:**

.. code-block:: bash

    pip install blast-radius-sdk

.. code-block:: python

    from blast_radius import BlastRadiusClient
    
    client = BlastRadiusClient(
        base_url="https://your-instance.com",
        api_token="your-api-token"
    )
    
    # Analyze attack paths
    paths = client.attack_paths.analyze(
        source_asset_id="web_server_001",
        target_asset_ids=["database_001"]
    )

**JavaScript SDK:**

.. code-block:: bash

    npm install @blast-radius/sdk

.. code-block:: javascript

    import { BlastRadiusClient } from '@blast-radius/sdk';
    
    const client = new BlastRadiusClient({
        baseUrl: 'https://your-instance.com',
        apiToken: 'your-api-token'
    });
    
    // Calculate blast radius
    const blastRadius = await client.attackPaths.calculateBlastRadius({
        sourceAssetId: 'compromised_server',
        maxDegrees: 3
    });

**Go SDK:**

.. code-block:: bash

    go get github.com/blast-radius/go-sdk

.. code-block:: go

    import "github.com/blast-radius/go-sdk/client"
    
    client := client.New(&client.Config{
        BaseURL:  "https://your-instance.com",
        APIToken: "your-api-token",
    })
    
    // Create attack scenario
    scenario, err := client.AttackPaths.CreateScenario(&AttackScenarioRequest{
        ScenarioName: "APT Campaign",
        ThreatActor:  "APT29",
        EntryPoints:  []string{"internet_gateway"},
        Objectives:   []string{"database_001"},
    })

Testing and Development
-----------------------

**API Testing:**

Use the interactive API documentation at ``/docs`` for testing endpoints.

**Sandbox Environment:**

A sandbox environment is available for testing::

    https://sandbox.blast-radius.com/api/v1/

**Mock Data:**

The sandbox includes realistic mock data for all endpoints.

**Postman Collection:**

Download the Postman collection for easy API testing:

`Download Postman Collection <https://github.com/forkrul/blast-radius/blob/master/docs/postman/blast-radius-api.json>`_

Support and Resources
---------------------

* **API Status**: `https://status.blast-radius.com <https://status.blast-radius.com>`_
* **GitHub Issues**: `Report API issues <https://github.com/forkrul/blast-radius/issues>`_
* **Community Forum**: Join developer discussions
* **API Changelog**: Track API changes and updates

Best Practices
--------------

1. **Use Appropriate HTTP Methods**: GET for retrieval, POST for creation, PUT/PATCH for updates
2. **Handle Rate Limits**: Implement exponential backoff for rate limit responses
3. **Validate Input**: Always validate request parameters before sending
4. **Use HTTPS**: All API communication must use HTTPS
5. **Store Tokens Securely**: Never expose API tokens in client-side code
6. **Monitor Usage**: Track API usage and performance metrics
7. **Handle Errors Gracefully**: Implement proper error handling and retry logic
8. **Use Webhooks**: Leverage webhooks for real-time updates instead of polling

Examples
--------

**Complete Workflow Example:**

.. code-block:: python

    # 1. Authenticate
    auth_response = requests.post(f"{api_base}/auth/login", json={
        "username": "security_analyst",
        "password": "secure_password"
    })
    token = auth_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # 2. Discover assets
    discovery_response = requests.post(f"{api_base}/discovery/scan", 
        headers=headers, json={"target": "10.0.0.0/24"})
    
    # 3. Analyze attack paths
    paths_response = requests.post(f"{api_base}/attack-paths/analyze",
        headers=headers, json={
            "source_asset_id": "internet_gateway",
            "max_path_length": 5
        })
    
    # 4. Calculate blast radius
    blast_response = requests.post(f"{api_base}/attack-paths/blast-radius",
        headers=headers, json={
            "source_asset_id": "compromised_server",
            "max_degrees": 3
        })
    
    # 5. Generate report
    report_response = requests.post(f"{api_base}/reports/generate",
        headers=headers, json={
            "type": "security_assessment",
            "include_attack_paths": True,
            "include_blast_radius": True
        })

This comprehensive API reference provides everything needed to integrate with the Blast-Radius Security Tool programmatically.
