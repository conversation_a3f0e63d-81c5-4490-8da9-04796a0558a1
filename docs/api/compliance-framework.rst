Compliance Framework API
=========================

The Compliance Framework API provides comprehensive endpoints for managing regulatory compliance, GDPR operations, audit logging, and data subject rights within the Blast-Radius Security Tool.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

The Compliance Framework supports:

* **GDPR Compliance**: Complete data subject rights management and consent tracking
* **Audit Logging**: Comprehensive audit trails with integrity verification
* **Least Privilege Access**: Time-based access management with approval workflows
* **Data Retention**: Configurable retention policies and automated cleanup
* **Regulatory Reporting**: Multi-framework compliance reporting and dashboards

Base URL
--------

All compliance endpoints are available under::

    /api/v1/compliance/

Authentication
--------------

All compliance endpoints require authentication and appropriate permissions::

    Authorization: Bearer <jwt-token>

Required permissions vary by endpoint and are documented in each section.

GDPR Compliance Endpoints
-------------------------

Consent Management
~~~~~~~~~~~~~~~~~~

**Record User Consent**

.. code-block:: http

    POST /api/v1/compliance/gdpr/consent
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "purpose": "ANALYTICS",
        "legal_basis": "CONSENT",
        "consent_text": "I agree to analytics processing",
        "version": "1.0",
        "expires_in_days": 365
    }

**Response:**

.. code-block:: json

    {
        "consent_id": "consent_12345",
        "status": "recorded",
        "expires_at": "2025-06-17T23:59:59Z",
        "message": "Consent recorded successfully"
    }

**Withdraw Consent**

.. code-block:: http

    DELETE /api/v1/compliance/gdpr/consent/{purpose}
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "status": "withdrawn",
        "message": "Consent withdrawn successfully",
        "effective_date": "2024-06-17T15:30:00Z"
    }

Data Subject Rights
~~~~~~~~~~~~~~~~~~~

**Submit Data Subject Request**

.. code-block:: http

    POST /api/v1/compliance/gdpr/data-subject-request
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "request_type": "ACCESS",
        "description": "Request copy of all personal data",
        "verification_method": "email"
    }

**Response:**

.. code-block:: json

    {
        "request_id": "dsr_67890",
        "status": "submitted",
        "estimated_completion": "2024-06-24T15:30:00Z",
        "verification_sent": true
    }

**Get Request Status**

.. code-block:: http

    GET /api/v1/compliance/gdpr/data-subject-request/{request_id}
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "request_id": "dsr_67890",
        "status": "in_progress",
        "request_type": "ACCESS",
        "submitted_at": "2024-06-17T15:30:00Z",
        "estimated_completion": "2024-06-24T15:30:00Z",
        "progress": {
            "data_collection": "completed",
            "verification": "completed",
            "preparation": "in_progress",
            "delivery": "pending"
        }
    }

Audit and Logging Endpoints
----------------------------

**Get Audit Events**

.. code-block:: http

    GET /api/v1/compliance/audit/events
    Authorization: Bearer <token>

Query Parameters:

* ``resource_type`` (string, optional): Filter by resource type
* ``resource_id`` (string, optional): Filter by specific resource ID
* ``user_id`` (string, optional): Filter by user ID
* ``event_types`` (array, optional): Filter by event types
* ``start_time`` (datetime, optional): Start time for date range
* ``end_time`` (datetime, optional): End time for date range
* ``limit`` (integer, optional): Maximum results (1-1000, default: 100)

**Response:**

.. code-block:: json

    {
        "events": [
            {
                "event_id": "evt_12345",
                "timestamp": "2024-06-17T15:30:00Z",
                "event_type": "USER_LOGIN",
                "user_id": "user_123",
                "resource_type": "user",
                "resource_id": "user_123",
                "details": {
                    "ip_address": "*************",
                    "user_agent": "Mozilla/5.0...",
                    "success": true
                },
                "integrity_hash": "sha256:abc123..."
            }
        ],
        "total": 1250,
        "has_more": true,
        "next_cursor": "eyJpZCI6MTIzNDU2Nzg5MH0"
    }

**Generate Audit Report**

.. code-block:: http

    POST /api/v1/compliance/audit/report
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "report_type": "COMPLIANCE_SUMMARY",
        "start_date": "2024-06-01T00:00:00Z",
        "end_date": "2024-06-17T23:59:59Z",
        "include_details": true,
        "format": "PDF"
    }

**Response:**

.. code-block:: json

    {
        "report_id": "rpt_98765",
        "status": "generating",
        "estimated_completion": "2024-06-17T16:00:00Z",
        "download_url": null
    }

Least Privilege Access Management
----------------------------------

**Request Access**

.. code-block:: http

    POST /api/v1/compliance/access/request
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "resource_type": "database",
        "resource_id": "prod-db-01",
        "access_types": ["READ"],
        "justification": "BUSINESS_NEED",
        "business_justification": "Monthly financial reporting",
        "duration_hours": 24,
        "emergency": false
    }

**Response:**

.. code-block:: json

    {
        "request_id": "acc_54321",
        "status": "pending_approval",
        "approver_assigned": "manager_123",
        "estimated_approval": "2024-06-17T17:00:00Z",
        "auto_expire_at": "2024-06-18T16:00:00Z"
    }

**Approve Access Request**

.. code-block:: http

    POST /api/v1/compliance/access/approve/{request_id}
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "approved": true,
        "approval_notes": "Approved for monthly reporting",
        "modified_duration_hours": 12
    }

**Response:**

.. code-block:: json

    {
        "request_id": "acc_54321",
        "status": "approved",
        "access_granted_at": "2024-06-17T16:00:00Z",
        "access_expires_at": "2024-06-18T04:00:00Z",
        "approval_notes": "Approved for monthly reporting"
    }

Data Retention Management
-------------------------

**Get Retention Policies**

.. code-block:: http

    GET /api/v1/compliance/retention/policies
    Authorization: Bearer <token>

**Response:**

.. code-block:: json

    {
        "policies": [
            {
                "policy_id": "pol_123",
                "name": "User Data Retention",
                "data_type": "user_data",
                "retention_period_days": 2555,
                "auto_delete": true,
                "legal_basis": "GDPR Article 5(1)(e)",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ]
    }

**Execute Retention Cleanup**

.. code-block:: http

    POST /api/v1/compliance/retention/cleanup
    Content-Type: application/json
    Authorization: Bearer <token>

    {
        "policy_ids": ["pol_123"],
        "dry_run": false,
        "force": false
    }

**Response:**

.. code-block:: json

    {
        "cleanup_id": "cleanup_789",
        "status": "running",
        "estimated_completion": "2024-06-17T17:00:00Z",
        "records_to_process": 15000
    }

Error Handling
--------------

The Compliance API uses standard HTTP status codes and provides detailed error information:

**400 Bad Request:**

.. code-block:: json

    {
        "error": {
            "code": "INVALID_CONSENT_PURPOSE",
            "message": "Invalid consent purpose specified",
            "details": {
                "field": "purpose",
                "allowed_values": ["ANALYTICS", "MARKETING", "FUNCTIONAL"]
            }
        }
    }

**403 Forbidden:**

.. code-block:: json

    {
        "error": {
            "code": "INSUFFICIENT_PERMISSIONS",
            "message": "User lacks required permissions for this operation",
            "details": {
                "required_permission": "MANAGE_GDPR_REQUESTS",
                "user_permissions": ["VIEW_AUDIT_LOGS"]
            }
        }
    }

**409 Conflict:**

.. code-block:: json

    {
        "error": {
            "code": "CONSENT_ALREADY_EXISTS",
            "message": "Consent already recorded for this purpose",
            "details": {
                "existing_consent_id": "consent_12345",
                "recorded_at": "2024-06-01T10:00:00Z"
            }
        }
    }

Rate Limits
-----------

Compliance endpoints have specific rate limits:

* **GDPR Operations**: 60 requests per minute
* **Audit Queries**: 120 requests per minute
* **Access Requests**: 30 requests per minute
* **Retention Operations**: 10 requests per minute

Best Practices
--------------

1. **Consent Management**: Always verify consent before processing personal data
2. **Audit Logging**: Regularly review audit logs for compliance monitoring
3. **Access Control**: Use least privilege principles and time-based access
4. **Data Retention**: Implement automated retention policies
5. **Error Handling**: Properly handle compliance-related errors
6. **Documentation**: Maintain detailed records of all compliance operations

Security Considerations
-----------------------

* All compliance data is encrypted at rest and in transit
* Audit logs include integrity verification hashes
* Access requests require multi-level approval for sensitive resources
* Data subject requests include identity verification
* Retention operations include safeguards against accidental deletion

Integration Examples
--------------------

**Python SDK Example:**

.. code-block:: python

    from blast_radius import BlastRadiusClient
    
    client = BlastRadiusClient(api_token="your-token")
    
    # Record GDPR consent
    consent = client.compliance.record_consent(
        purpose="ANALYTICS",
        legal_basis="CONSENT",
        consent_text="I agree to analytics processing"
    )
    
    # Submit data subject request
    request = client.compliance.submit_data_subject_request(
        request_type="ACCESS",
        description="Request copy of all personal data"
    )
    
    # Query audit events
    events = client.compliance.get_audit_events(
        event_types=["USER_LOGIN", "DATA_ACCESS"],
        start_time="2024-06-01T00:00:00Z"
    )

This comprehensive API enables full compliance management within your security operations.
