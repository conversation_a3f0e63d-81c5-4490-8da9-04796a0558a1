Asset Management API
====================

The Asset Management API provides comprehensive functionality for discovering, managing, and analyzing security assets within your infrastructure.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
--------

Assets represent any component in your infrastructure that can be analyzed for security risks, including:

- **Servers**: Physical and virtual machines
- **Network Devices**: Routers, switches, firewalls
- **Applications**: Web applications, services, APIs
- **Databases**: SQL and NoSQL databases
- **Cloud Resources**: AWS, Azure, GCP resources
- **Containers**: Docker containers, Kubernetes pods

Asset Discovery
---------------

Discover Assets
~~~~~~~~~~~~~~~

.. http:post:: /api/v1/assets/discovery/scan

   Initiate asset discovery scan for specified targets.

   **Request:**

   .. code-block:: json

      {
        "targets": ["10.0.0.0/24", "example.com"],
        "scan_type": "comprehensive",
        "discovery_engines": ["nmap", "aws", "azure"],
        "options": {
          "port_scan": true,
          "service_detection": true,
          "os_detection": true,
          "vulnerability_scan": false
        }
      }

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "scan_id": "scan_12345",
          "status": "running",
          "targets": ["10.0.0.0/24", "example.com"],
          "estimated_duration": "15 minutes",
          "started_at": "2024-01-15T10:30:00Z"
        }
      }

   :statuscode 201: Scan initiated successfully
   :statuscode 400: Invalid scan parameters
   :statuscode 403: Insufficient permissions

Get Discovery Status
~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/assets/discovery/scan/(scan_id)

   Get the status and progress of an asset discovery scan.

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "scan_id": "scan_12345",
          "status": "completed",
          "progress": 100,
          "assets_discovered": 45,
          "assets_updated": 12,
          "started_at": "2024-01-15T10:30:00Z",
          "completed_at": "2024-01-15T10:45:00Z",
          "summary": {
            "servers": 15,
            "network_devices": 8,
            "applications": 22
          }
        }
      }

Asset Management
----------------

List Assets
~~~~~~~~~~~

.. http:get:: /api/v1/assets

   Retrieve a paginated list of assets with optional filtering.

   **Query Parameters:**

   - ``limit`` (integer): Number of assets per page (max: 100, default: 20)
   - ``cursor`` (string): Pagination cursor
   - ``asset_type`` (string): Filter by asset type
   - ``environment`` (string): Filter by environment
   - ``risk_level`` (string): Filter by risk level (low, medium, high, critical)
   - ``search`` (string): Search assets by name or description
   - ``sort`` (string): Sort order (e.g., "risk_score:desc,name:asc")

   **Example Request:**

   .. code-block:: http

      GET /api/v1/assets?asset_type=server&environment=production&limit=50

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": [
          {
            "id": "asset_001",
            "name": "web-server-01",
            "asset_type": "server",
            "ip_address": "**********",
            "hostname": "web01.example.com",
            "environment": "production",
            "risk_score": 7.5,
            "criticality": "high",
            "last_seen": "2024-01-15T10:30:00Z",
            "metadata": {
              "os": "Ubuntu 20.04",
              "services": ["nginx", "mysql"],
              "tags": ["web", "frontend"]
            }
          }
        ],
        "pagination": {
          "limit": 50,
          "has_more": true,
          "next_cursor": "eyJpZCI6ImFzc2V0XzAwMSJ9",
          "total_count": 1250
        }
      }

Get Asset Details
~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/assets/(asset_id)

   Retrieve detailed information about a specific asset.

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "id": "asset_001",
          "name": "web-server-01",
          "asset_type": "server",
          "ip_address": "**********",
          "hostname": "web01.example.com",
          "environment": "production",
          "risk_score": 7.5,
          "criticality": "high",
          "created_at": "2024-01-10T08:00:00Z",
          "updated_at": "2024-01-15T10:30:00Z",
          "last_seen": "2024-01-15T10:30:00Z",
          "metadata": {
            "os": "Ubuntu 20.04",
            "os_version": "20.04.3 LTS",
            "architecture": "x86_64",
            "services": [
              {
                "name": "nginx",
                "port": 80,
                "version": "1.18.0",
                "status": "running"
              },
              {
                "name": "mysql",
                "port": 3306,
                "version": "8.0.28",
                "status": "running"
              }
            ],
            "vulnerabilities": [
              {
                "cve": "CVE-2021-44228",
                "severity": "critical",
                "score": 10.0,
                "description": "Log4j RCE vulnerability"
              }
            ],
            "tags": ["web", "frontend", "critical"],
            "owner": "web-team",
            "location": "datacenter-east"
          },
          "relationships": {
            "connects_to": ["asset_002", "asset_003"],
            "depends_on": ["asset_004"],
            "parent": "asset_005"
          }
        }
      }

Create Asset
~~~~~~~~~~~~

.. http:post:: /api/v1/assets

   Create a new asset manually.

   **Request:**

   .. code-block:: json

      {
        "name": "new-server-01",
        "asset_type": "server",
        "ip_address": "**********",
        "hostname": "new01.example.com",
        "environment": "staging",
        "criticality": "medium",
        "metadata": {
          "os": "CentOS 8",
          "owner": "dev-team",
          "tags": ["development", "testing"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "id": "asset_new_001",
          "name": "new-server-01",
          "asset_type": "server",
          "ip_address": "**********",
          "hostname": "new01.example.com",
          "environment": "staging",
          "criticality": "medium",
          "risk_score": 0.0,
          "created_at": "2024-01-15T11:00:00Z",
          "updated_at": "2024-01-15T11:00:00Z"
        }
      }

Update Asset
~~~~~~~~~~~~

.. http:put:: /api/v1/assets/(asset_id)

   Update an existing asset's information.

   **Request:**

   .. code-block:: json

      {
        "criticality": "high",
        "metadata": {
          "owner": "security-team",
          "tags": ["critical", "production", "web"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "id": "asset_001",
          "name": "web-server-01",
          "criticality": "high",
          "updated_at": "2024-01-15T11:15:00Z"
        }
      }

Delete Asset
~~~~~~~~~~~~

.. http:delete:: /api/v1/assets/(asset_id)

   Delete an asset from the inventory.

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "message": "Asset deleted successfully"
      }

   :statuscode 200: Asset deleted successfully
   :statuscode 404: Asset not found
   :statuscode 409: Asset has dependencies and cannot be deleted

Asset Relationships
-------------------

Get Asset Relationships
~~~~~~~~~~~~~~~~~~~~~~~

.. http:get:: /api/v1/assets/(asset_id)/relationships

   Get all relationships for a specific asset.

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "asset_id": "asset_001",
          "relationships": [
            {
              "type": "connects_to",
              "target_asset_id": "asset_002",
              "target_asset_name": "database-01",
              "relationship_metadata": {
                "protocol": "TCP",
                "port": 3306,
                "connection_type": "database"
              }
            },
            {
              "type": "depends_on",
              "target_asset_id": "asset_003",
              "target_asset_name": "load-balancer-01",
              "relationship_metadata": {
                "dependency_type": "network"
              }
            }
          ]
        }
      }

Create Asset Relationship
~~~~~~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/assets/(asset_id)/relationships

   Create a new relationship between assets.

   **Request:**

   .. code-block:: json

      {
        "type": "connects_to",
        "target_asset_id": "asset_004",
        "metadata": {
          "protocol": "HTTPS",
          "port": 443,
          "connection_type": "api"
        }
      }

Asset Risk Assessment
---------------------

Calculate Asset Risk
~~~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/assets/(asset_id)/risk/calculate

   Calculate or recalculate the risk score for an asset.

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "asset_id": "asset_001",
          "risk_score": 8.5,
          "risk_level": "high",
          "risk_factors": [
            {
              "factor": "critical_vulnerabilities",
              "score": 9.0,
              "weight": 0.4,
              "description": "Asset has critical vulnerabilities"
            },
            {
              "factor": "network_exposure",
              "score": 7.0,
              "weight": 0.3,
              "description": "Asset is exposed to internet"
            },
            {
              "factor": "data_sensitivity",
              "score": 9.0,
              "weight": 0.3,
              "description": "Asset handles sensitive data"
            }
          ],
          "calculated_at": "2024-01-15T11:30:00Z"
        }
      }

Asset Bulk Operations
--------------------

Bulk Update Assets
~~~~~~~~~~~~~~~~~~

.. http:post:: /api/v1/assets/bulk/update

   Update multiple assets in a single operation.

   **Request:**

   .. code-block:: json

      {
        "assets": [
          {
            "id": "asset_001",
            "criticality": "high",
            "metadata": {"owner": "security-team"}
          },
          {
            "id": "asset_002", 
            "environment": "production"
          }
        ]
      }

   **Response:**

   .. code-block:: json

      {
        "status": "success",
        "data": {
          "updated_count": 2,
          "failed_count": 0,
          "results": [
            {"asset_id": "asset_001", "status": "updated"},
            {"asset_id": "asset_002", "status": "updated"}
          ]
        }
      }

Export Assets
~~~~~~~~~~~~~

.. http:get:: /api/v1/assets/export

   Export asset data in various formats.

   **Query Parameters:**

   - ``format`` (string): Export format (json, csv, xlsx)
   - ``filter`` (string): Asset filter criteria
   - ``fields`` (string): Comma-separated list of fields to include

   **Example:**

   .. code-block:: http

      GET /api/v1/assets/export?format=csv&filter=environment:production&fields=id,name,ip_address,risk_score

Error Codes
-----------

Asset-specific error codes:

- ``ASSET_NOT_FOUND``: Asset with specified ID not found
- ``ASSET_ALREADY_EXISTS``: Asset with same identifier already exists
- ``INVALID_ASSET_TYPE``: Unsupported asset type specified
- ``DISCOVERY_ENGINE_ERROR``: Asset discovery engine failed
- ``RELATIONSHIP_CONFLICT``: Relationship would create circular dependency
- ``BULK_OPERATION_PARTIAL_FAILURE``: Some assets in bulk operation failed

Examples
--------

**Python SDK Example:**

.. code-block:: python

   from blast_radius import BlastRadiusClient
   
   client = BlastRadiusClient(api_token="your-token")
   
   # Discover assets
   scan = client.assets.discover(
       targets=["10.0.0.0/24"],
       scan_type="comprehensive"
   )
   
   # Wait for completion
   scan.wait_for_completion()
   
   # List discovered assets
   assets = client.assets.list(
       asset_type="server",
       environment="production"
   )
   
   # Update asset criticality
   asset = client.assets.get("asset_001")
   asset.update(criticality="high")

**JavaScript Example:**

.. code-block:: javascript

   import { BlastRadiusClient } from '@blast-radius/sdk';
   
   const client = new BlastRadiusClient({
     apiToken: 'your-token'
   });
   
   // Create asset
   const asset = await client.assets.create({
     name: 'web-server-02',
     assetType: 'server',
     ipAddress: '**********',
     environment: 'production'
   });
   
   // Calculate risk
   const riskAssessment = await client.assets.calculateRisk(asset.id);
   console.log(`Risk Score: ${riskAssessment.riskScore}`);

This comprehensive Asset Management API enables full lifecycle management of security assets within your infrastructure.
