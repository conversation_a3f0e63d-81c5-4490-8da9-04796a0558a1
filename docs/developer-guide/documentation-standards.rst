Documentation Standards
======================

This guide outlines the documentation standards for the Blast-Radius Security Tool project.

Overview
--------

High-quality documentation is essential for maintainability, onboarding, and user adoption. 
This project follows strict documentation standards to ensure consistency and completeness.

Docstring Standards
------------------

Python Docstring Format
~~~~~~~~~~~~~~~~~~~~~~~

We use Google-style docstrings with type hints. All public functions, classes, and modules must have docstrings.

**Function Example:**

.. code-block:: python

    def analyze_attack_path(
        source_asset: str, 
        target_asset: str, 
        max_depth: int = 5
    ) -> List[AttackPath]:
        """Analyze potential attack paths between two assets.
        
        This function performs graph traversal to identify all possible
        attack paths from a source asset to a target asset within the
        specified maximum depth.
        
        Args:
            source_asset: The identifier of the source asset.
            target_asset: The identifier of the target asset.
            max_depth: Maximum path depth to explore. Defaults to 5.
            
        Returns:
            A list of AttackPath objects representing possible attack vectors.
            Returns empty list if no paths found.
            
        Raises:
            AssetNotFoundError: If source or target asset doesn't exist.
            GraphAnalysisError: If graph analysis fails.
            
        Example:
            >>> paths = analyze_attack_path("server-01", "database-01")
            >>> len(paths)
            3
            >>> paths[0].risk_score
            8.5
        """

Documentation Coverage Requirements
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

- **100%** coverage for public APIs
- **90%** coverage for internal modules
- **80%** coverage for test utilities

Quality Criteria
~~~~~~~~~~~~~~~~

All docstrings must include:

1. **Clear description** of purpose and behavior
2. **Complete parameter documentation** with types
3. **Return value documentation** with types
4. **Exception documentation** for all raised exceptions
5. **Usage examples** for complex functions
6. **Type hints** in function signatures

RST Documentation Standards
---------------------------

File Structure
~~~~~~~~~~~~~

.. code-block:: text

    docs/
    ├── index.rst                 # Main documentation index
    ├── installation.rst          # Installation guide
    ├── quick-start-guide.rst     # Getting started
    ├── configuration.rst         # Configuration reference
    ├── api/                      # API documentation
    │   ├── index.rst
    │   ├── authentication.rst
    │   └── attack-path-analysis.rst
    ├── user-guides/              # User guides by role
    │   ├── index.rst
    │   ├── soc-operators.rst
    │   ├── security-architects.rst
    │   ├── red-team-members.rst
    │   └── purple-team-members.rst
    ├── developer-guide/          # Developer documentation
    │   ├── index.rst
    │   ├── architecture.rst
    │   ├── contributing.rst
    │   └── testing.rst
    └── troubleshooting/          # Troubleshooting guides
        ├── index.rst
        └── faq.rst

Testing Documentation
--------------------

Documentation Tests
~~~~~~~~~~~~~~~~~~

All documentation must pass:

1. **Build tests** - Documentation builds without errors
2. **Link tests** - All links are valid and accessible  
3. **Example tests** - All code examples execute correctly
4. **Coverage tests** - Documentation coverage meets thresholds

**Running Documentation Tests:**

.. code-block:: bash

    # Run all documentation tests
    make test-docs
    
    # Run specific test types
    make test-docs-build      # Build validation
    make test-docs-links      # Link checking
    make test-docs-examples   # Code example validation
    make test-docs-coverage   # Coverage analysis

Quality Metrics
~~~~~~~~~~~~~~

Documentation quality is measured by:

- **Build Success Rate**: 100% (no build failures)
- **Link Validity**: 95%+ (broken links < 5%)
- **Docstring Coverage**: 90%+ for public APIs
- **Example Coverage**: 80%+ of functions have examples
- **Update Frequency**: Documentation updated with every release

Best Practices
--------------

Writing Effective Documentation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

1. **Know your audience** - Write for specific user roles
2. **Start with examples** - Show before explaining
3. **Use progressive disclosure** - Simple to complex
4. **Maintain consistency** - Follow established patterns
5. **Test everything** - Validate all examples and procedures

For questions about documentation standards, please refer to the contributing guide.
